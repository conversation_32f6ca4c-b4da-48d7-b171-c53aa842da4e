// Cashfree specific types and interfaces

export interface CashfreeConfig {
  appId: string;
  secretKey: string;
  environment: "sandbox" | "production";
  baseURL?: string;
}

export interface CashfreeResponse<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

// Customer interfaces
export interface CashfreeCreateCustomerRequest {
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
}

export interface CashfreeCustomerResponse {
  customer_id: string;
  customer_name?: string;
  customer_email?: string;
  customer_phone?: string;
  customer_bank_account_number?: string;
  customer_bank_ifsc?: string;
  customer_bank_code?: string;
  created_at?: string;
  updated_at?: string;
}

// Order interfaces
export interface CashfreeCreateOrderRequest {
  order_id: string;
  order_amount: number;
  order_currency: string;
  customer_details: {
    customer_id: string;
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
  };
  order_meta?: {
    return_url?: string;
    notify_url?: string;
    payment_methods?: string;
  };
  order_note?: string;
  order_tags?: Record<string, string>;
}

export interface CashfreeOrderResponse {
  cf_order_id: string;
  order_id: string;
  entity: string;
  order_currency: string;
  order_amount: number;
  order_expiry_time: string;
  customer_details: {
    customer_id: string;
    customer_name?: string;
    customer_email?: string;
    customer_phone?: string;
  };
  order_meta?: {
    return_url?: string;
    notify_url?: string;
    payment_methods?: string;
  };
  settlements: {
    url: string;
  };
  payments: {
    url: string;
  };
  refunds: {
    url: string;
  };
  order_status: string;
  order_token: string;
  order_note?: string;
  order_tags?: Record<string, string>;
  created_at: string;
}

// Payment interfaces
export interface CashfreePaymentDetails {
  cf_payment_id: string;
  order_id: string;
  entity: string;
  payment_currency: string;
  payment_amount: number;
  payment_time: string;
  payment_completion_time: string;
  payment_status: string;
  payment_message: string;
  bank_reference: string;
  auth_id: string;
  payment_method: {
    card?: {
      channel: string;
      card_number: string;
      card_network: string;
      card_type: string;
      card_country: string;
      card_bank_name: string;
    };
    netbanking?: {
      channel: string;
      netbanking_bank_code: string;
      netbanking_bank_name: string;
    };
    upi?: {
      channel: string;
      upi_id: string;
    };
    wallet?: {
      channel: string;
      wallet_name: string;
    };
  };
}

// Beneficiary interfaces (equivalent to contacts)
export interface CashfreeCreateBeneficiaryRequest {
  bene_id: string;
  name: string;
  email?: string;
  phone?: string;
  address1?: string;
  city?: string;
  state?: string;
  pincode?: string;
  bank_account?: string;
  ifsc?: string;
  vpa?: string;
}

export interface CashfreeBeneficiaryResponse {
  bene_id: string;
  name: string;
  email?: string;
  phone?: string;
  address1?: string;
  city?: string;
  state?: string;
  pincode?: string;
  bank_account?: string;
  ifsc?: string;
  vpa?: string;
  status: string;
  added_on: string;
}

// Transfer interfaces (equivalent to payouts)
export interface CashfreeCreateTransferRequest {
  bene_id: string;
  amount: number;
  transfer_id: string;
  transfer_mode?:
    | "banktransfer"
    | "imps"
    | "neft"
    | "rtgs"
    | "upi"
    | "paytm"
    | "amazonpay"
    | "card"
    | "cardupi"; // banktransfer, imps, neft, rtgs, upi, paytm, amazonpay, card, cardupi
  remarks?: string;
  fundsource_id: string;
}

export interface CashfreeTransferResponse {
  cf_transfer_id: string;
  transfer_id: string;
  beneficiary_details: { beneficiary_id: string };
  transfer_amount: number;
  transfer_service_charge: number;
  transfer_service_tax: number;
  status: string;
  transfer_mode: string;
  fundsource_id: string;
  transfer_utr: string;
  added_on: string;
  updated_on: string;
}

// Settlement interfaces
export interface CashfreeSettlementResponse {
  cf_settlement_id: string;
  settlement_id: string;
  settlement_currency: string;
  settlement_amount: number;
  settlement_status: string;
  settlement_charges: number;
  settlement_tax: number;
  settlement_utr: string;
  settlement_time: string;
  service_charge: number;
  service_tax: number;
  settlement_type: string;
  created_at: string;
}

// Webhook interfaces
export interface CashfreeWebhookData {
  type: string;
  order_id: string;
  cf_order_id: string;
  payment_details?: CashfreePaymentDetails;
  settlement_details?: CashfreeSettlementResponse;
  transfer_details?: CashfreeTransferResponse;
  signature: string;
  timestamp: string;
}

// Error interfaces
export interface CashfreeError {
  message: string;
  code: string;
  type: string;
}

export interface CashfreeErrorResponse {
  status: string;
  message: string;
  code: string;
  type: string;
}
