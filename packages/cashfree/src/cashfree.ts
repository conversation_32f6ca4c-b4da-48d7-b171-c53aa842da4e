import { AxiosError } from "axios";
import {
  Cashfree as CashfreePG,
  CFEnvironment,
  CustomerEntity,
  OrderEntity,
} from "cashfree-pg";

import { CashfreeClient } from "./client";
import {
  CashfreeBeneficiaryResponse,
  CashfreeConfig,
  CashfreeCreateBeneficiaryRequest,
  CashfreeCreateCustomerRequest,
  CashfreeCreateOrderRequest,
  CashfreeCreateTransferRequest,
  CashfreePaymentDetails,
  CashfreeResponse,
  CashfreeSettlementResponse,
  CashfreeTransferResponse,
} from "./types";

export class Cashfree {
  private client: CashfreeClient;
  private cashfreePG: CashfreePG;

  constructor(config: CashfreeConfig) {
    this.client = new CashfreeClient(config);

    // Initialize Cashfree PG SDK for backend operations
    // CashfreePG.XClientId = config.appId;
    // CashfreePG.XClientSecret = config.secretKey;
    const environment =
      config.environment === "production"
        ? CFEnvironment.PRODUCTION
        : CFEnvironment.SANDBOX;

    this.cashfreePG = new CashfreePG(
      environment,
      config.appId,
      config.secretKey,
    );
  }

  // Customer API
  async createCustomer(
    customer: CashfreeCreateCustomerRequest,
  ): Promise<CashfreeResponse<CustomerEntity>> {
    try {
      // Use official Cashfree PG SDK for customer creation
      const request = {
        // customer_id: customer.customer_id,
        customer_name: customer.customer_name || "",
        customer_email: customer.customer_email || "",
        customer_phone: customer.customer_phone || "",
      };

      const response = await this.cashfreePG.PGCreateCustomer(request);

      return {
        data: response.data,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      console.error("Cashfree createCustomer error", JSON.stringify(error));
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  // Orders API
  async createOrder(order: CashfreeCreateOrderRequest): Promise<{
    data: OrderEntity | null;
    isLoading: boolean;
    error: Error | null;
  }> {
    try {
      // Use official Cashfree PG SDK for order creation
      const request = {
        order_id: order.order_id,
        order_amount: order.order_amount,
        order_currency: order.order_currency,
        customer_details: {
          customer_id: order.customer_details.customer_id,
          customer_name: order.customer_details.customer_name,
          customer_email: order.customer_details.customer_email,
          customer_phone: order.customer_details.customer_phone,
        },
        order_meta: order.order_meta,
        order_note: order.order_note,
      };

      console.log("Cashfree createOrder request", JSON.stringify(request));

      const response = await this.cashfreePG.PGCreateOrder(request);

      if (response.status !== 200 || !response.data) {
        throw new Error("Failed to create order");
      }

      console.log(
        "Cashfree createOrder response",
        JSON.stringify(response.data),
      );

      return {
        data: response.data,
        isLoading: false,
        error: null,
      };
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error(
          "Cashfree createOrder error",
          JSON.stringify(error.response?.data),
        );
      } else {
        console.error("Cashfree createOrder error", JSON.stringify(error));
      }
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  async getOrder(orderId: string): Promise<{
    data: OrderEntity | null;
    isLoading: boolean;
    error: Error | null;
  }> {
    try {
      const data = await this.cashfreePG.PGFetchOrder(orderId);

      if (data.status !== 200 || !data.data) {
        throw new Error("Failed to fetch order");
      }

      return { data: data.data, isLoading: false, error: null };
      // const data = await this.client.request<CashfreeOrderResponse>(
      //   "GET",
      //   `/pg/orders/${orderId}`,
      // );
      // return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Payments API
  async getPaymentDetails(
    orderId: string,
    cfPaymentId?: string,
  ): Promise<CashfreeResponse<CashfreePaymentDetails[]>> {
    try {
      const endpoint = cfPaymentId
        ? `/pg/orders/${orderId}/payments/${cfPaymentId}`
        : `/pg/orders/${orderId}/payments`;

      const data = await this.client.request<CashfreePaymentDetails[]>(
        "GET",
        endpoint,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Beneficiaries API (equivalent to contacts)
  async createBeneficiary(
    beneficiary: CashfreeCreateBeneficiaryRequest,
  ): Promise<CashfreeResponse<CashfreeBeneficiaryResponse>> {
    try {
      // Use official Cashfree PG SDK for beneficiary creation
      const request = {
        beneId: beneficiary.bene_id,
        name: beneficiary.name,
        email: beneficiary.email,
        phone: beneficiary.phone,
        bankAccount: beneficiary.bank_account,
        ifsc: beneficiary.ifsc,
        address1: beneficiary.address1,
        city: beneficiary.city,
        state: beneficiary.state,
        pincode: beneficiary.pincode,
      };

      const response = await this.client.request<CashfreeBeneficiaryResponse>(
        "POST",
        `payout/beneficiary`,
        request,
      );

      return {
        data: response as CashfreeBeneficiaryResponse,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  async getBeneficiary(
    beneId: string,
  ): Promise<CashfreeResponse<CashfreeBeneficiaryResponse>> {
    try {
      const data = await this.client.request<CashfreeBeneficiaryResponse>(
        "GET",
        `/payout/beneficiary/${beneId}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async removeBeneficiary(
    beneId: string,
  ): Promise<CashfreeResponse<{ status: string; message: string }>> {
    try {
      const data = await this.client.request<{
        status: string;
        message: string;
      }>("DELETE", "/payout/beneficiary?beneficiary_id=${beneId}");
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Transfers API (equivalent to payouts)
  async createTransfer(
    transfer: CashfreeCreateTransferRequest,
  ): Promise<CashfreeResponse<CashfreeTransferResponse>> {
    try {
      // Use official Cashfree PG SDK for transfer creation
      const request = {
        beneficiary_details: {
          beneficiary_id: transfer.bene_id,
        },
        transfer_amount: transfer.amount,
        transfer_id: transfer.transfer_id,
        // transfer_currency: 'INR',
        transfer_mode: transfer.transfer_mode, // banktransfer, imps, neft, rtgs, upi, paytm, amazonpay, card, cardupi
        transfer_remarks: transfer.remarks,
        fundsource_id: transfer.fundsource_id, // Cashfree fund source id to be fetched from env
      };

      const response = await this.client.request<CashfreeTransferResponse>(
        "POST",
        `payout/transfer`,
        request,
      );

      return {
        data: response as CashfreeTransferResponse,
        isLoading: false,
        error: null,
      };
    } catch (error) {
      return {
        data: null,
        isLoading: false,
        error: error as Error,
      };
    }
  }

  async getTransferStatus(
    transferId: string,
  ): Promise<CashfreeResponse<CashfreeTransferResponse>> {
    try {
      const data = await this.client.request<CashfreeTransferResponse>(
        "GET",
        `/payout/transfers?transfer_id=${transferId}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Settlements API
  async getSettlements(
    maxReturn?: number,
    lastId?: string,
  ): Promise<CashfreeResponse<CashfreeSettlementResponse[]>> {
    try {
      const params = new URLSearchParams();
      if (maxReturn) params.append("maxReturn", maxReturn.toString());
      if (lastId) params.append("lastId", lastId);

      const data = await this.client.request<CashfreeSettlementResponse[]>(
        "GET",
        `/pg/settlements?${params.toString()}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Verification methods
  verifyWebhookSignature(
    rawBody: string,
    timestamp: string,
    signature: string,
  ): boolean {
    return this.client.verifyWebhookSignature(rawBody, timestamp, signature);
  }

  verifyPaymentSignature(
    orderId: string,
    orderAmount: number,
    orderCurrency: string,
    signature: string,
    customerEmail?: string,
    customerName?: string,
    customerPhone?: string,
  ): boolean {
    return this.client.verifyPaymentSignature(
      orderId,
      orderAmount,
      orderCurrency,
      signature,
      customerEmail,
      customerName,
      customerPhone,
    );
  }

  // Utility methods
  generatePaymentSignature(
    orderId: string,
    orderAmount: number,
    orderCurrency: string,
    customerEmail?: string,
    customerName?: string,
    customerPhone?: string,
  ): string {
    return this.client.generatePaymentSignature(
      orderId,
      orderAmount,
      orderCurrency,
      customerEmail,
      customerName,
      customerPhone,
    );
  }
}
