# Cashfree TypeScript SDK

A TypeScript SDK for integrating with Cashfree APIs, focusing on Orders, Payments, Payouts, and Settlements.

## Installation

```bash
npm install @acme/cashfree-sdk
# or
yarn add @acme/cashfree-sdk
# or
pnpm add @acme/cashfree-sdk
```

## Usage

### Initialize the client

```typescript
import { Cashfree } from "@acme/cashfree-sdk";

const cashfree = new Cashfree({
  appId: "YOUR_CASHFREE_APP_ID",
  secretKey: "YOUR_CASHFREE_SECRET_KEY",
  environment: "sandbox", // or "production"
});
```

### Working with Orders

```typescript
// Create an order
const { data, isLoading, error } = await cashfree.createOrder({
  order_id: "order_123",
  order_amount: 100.00,
  order_currency: "INR",
  customer_details: {
    customer_id: "customer_123",
    customer_name: "<PERSON>",
    customer_email: "<EMAIL>",
    customer_phone: "+************",
  },
});

// Get order details
const orderDetails = await cashfree.getOrder("order_123");
```

### Working with Payments

```typescript
// Get payment details for an order
const payments = await cashfree.getPaymentDetails("order_123");

// Verify payment signature
const isValid = cashfree.verifyPaymentSignature(
  "order_123",
  100.00,
  "INR",
  "signature_from_frontend",
  "<EMAIL>",
  "John Doe",
  "+************"
);
```

### Working with Beneficiaries (Payouts)

```typescript
// Create a beneficiary
const beneficiary = await cashfree.createBeneficiary({
  bene_id: "beneficiary_123",
  name: "John Doe",
  email: "<EMAIL>",
  phone: "+************",
  bank_account: "**********",
  ifsc: "HDFC0000123",
});

// Create a transfer
const transfer = await cashfree.createTransfer({
  bene_id: "beneficiary_123",
  amount: 100.00,
  transfer_id: "transfer_123",
  transfer_mode: "banktransfer",
  remarks: "Payment for services",
});
```

### Webhook Verification

```typescript
// Verify webhook signature
const isValidWebhook = cashfree.verifyWebhookSignature(
  rawRequestBody,
  timestamp,
  signature
);
```

## API Reference

### Orders
- `createOrder(order)` - Create a new order
- `getOrder(orderId)` - Get order details

### Payments
- `getPaymentDetails(orderId, cfPaymentId?)` - Get payment details

### Beneficiaries
- `createBeneficiary(beneficiary)` - Create a beneficiary
- `getBeneficiary(beneId)` - Get beneficiary details
- `removeBeneficiary(beneId)` - Remove a beneficiary

### Transfers
- `createTransfer(transfer)` - Create a transfer
- `getTransferStatus(transferId)` - Get transfer status

### Settlements
- `getSettlements(maxReturn?, lastId?)` - Get settlement details

### Verification
- `verifyWebhookSignature(rawBody, timestamp, signature)` - Verify webhook signature
- `verifyPaymentSignature(...)` - Verify payment signature

## Error Handling

All methods return a response object with the following structure:

```typescript
{
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}
```

Check the `error` field to handle any API errors.

## Environment

- **Sandbox**: Use for testing and development
- **Production**: Use for live transactions

Make sure to use the correct environment and credentials for your use case.
