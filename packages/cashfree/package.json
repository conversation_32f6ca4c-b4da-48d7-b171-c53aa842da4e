{"name": "@acme/cashfree-sdk", "version": "1.0.0", "description": "TypeScript SDK for Cashfree APIs (Orders, Payments, Payouts, and Settlements)", "main": "src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "prepare": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["cashfree", "payments", "api", "typescript", "sdk"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0", "cashfree-pg": "^5.0.8"}, "peerDependencies": {"typescript": "catalog:"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "catalog:"}}