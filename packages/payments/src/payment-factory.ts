import type { IPaymentGateway, PaymentConfig, PaymentGateway } from "./types";
import { CashfreeAdapter } from "./adapters/cashfree-adapter";
import { RazorpayAdapter } from "./adapters/razorpay-adapter";

export class PaymentFactory {
  private static instance: PaymentFactory;
  private config: PaymentConfig;
  private primaryGateway: IPaymentGateway | null = null;
  private fallbackGateway: IPaymentGateway | null = null;

  private constructor(config: PaymentConfig) {
    this.config = config;
    this.initializeGateways();
    console.log("PaymentFactory initialized with config:", this.config);
  }

  public static getInstance(config?: PaymentConfig): PaymentFactory {
    if (!PaymentFactory.instance) {
      if (!config) {
        throw new Error(
          "PaymentFactory must be initialized with config on first use",
        );
      }
      PaymentFactory.instance = new PaymentFactory(config);
    }
    return PaymentFactory.instance;
  }

  public static updateConfig(config: PaymentConfig): void {
    if (PaymentFactory.instance) {
      PaymentFactory.instance.config = config;
      PaymentFactory.instance.initializeGateways();
    }
  }

  private initializeGateways(): void {
    // Initialize primary gateway
    this.primaryGateway = this.createGateway(this.config.gateway);

    // Initialize fallback gateway if specified
    if (
      this.config.fallbackGateway &&
      this.config.fallbackGateway !== this.config.gateway
    ) {
      this.fallbackGateway = this.createGateway(this.config.fallbackGateway);
    }
  }

  private createGateway(gateway: PaymentGateway): IPaymentGateway {
    switch (gateway) {
      case "RAZORPAY":
        if (!this.config.razorpay) {
          throw new Error("Razorpay configuration is required");
        }
        return new RazorpayAdapter(this.config.razorpay);

      case "CASHFREE":
        if (!this.config.cashfree) {
          throw new Error("Cashfree configuration is required");
        }
        return new CashfreeAdapter(this.config.cashfree);

      default:
        throw new Error(`Unsupported payment gateway: ${gateway}`);
    }
  }

  public getPrimaryGateway(): IPaymentGateway {
    if (!this.primaryGateway) {
      throw new Error("Primary payment gateway not initialized");
    }
    return this.primaryGateway;
  }

  public getFallbackGateway(): IPaymentGateway | null {
    return this.fallbackGateway;
  }

  public getCurrentGateway(): PaymentGateway {
    return this.config.gateway;
  }

  public getFallbackGatewayType(): PaymentGateway | undefined {
    return this.config.fallbackGateway;
  }

  public async executeWithFallback<T>(
    operation: (gateway: IPaymentGateway) => Promise<T>,
    operationName: string = "payment operation",
  ): Promise<T> {
    try {
      console.log(
        `Executing ${operationName} with primary gateway: ${this.config.gateway}`,
      );
      const result = await operation(this.getPrimaryGateway());

      // Check if the result indicates an error (for PaymentResponse types)
      if (
        typeof result === "object" &&
        result !== null &&
        "error" in result &&
        result.error
      ) {
        throw result.error;
      }

      return result;
    } catch (primaryError) {
      console.error(
        `Primary gateway (${this.config.gateway}) failed for ${operationName}:`,
        primaryError,
      );

      if (this.fallbackGateway && this.config.fallbackGateway) {
        try {
          console.log(
            `Attempting ${operationName} with fallback gateway: ${this.config.fallbackGateway}`,
          );
          const result = await operation(this.fallbackGateway);

          // Check if the result indicates an error (for PaymentResponse types)
          if (
            typeof result === "object" &&
            result !== null &&
            "error" in result &&
            result.error
          ) {
            throw result.error;
          }

          console.log(
            `Fallback gateway (${this.config.fallbackGateway}) succeeded for ${operationName}`,
          );
          return result;
        } catch (fallbackError) {
          console.error(
            `Fallback gateway (${this.config.fallbackGateway}) also failed for ${operationName}:`,
            fallbackError,
          );
          throw new Error(
            `Both primary (${this.config.gateway}) and fallback (${this.config.fallbackGateway}) gateways failed. Primary error: ${primaryError}. Fallback error: ${fallbackError}`,
          );
        }
      }

      throw primaryError;
    }
  }

  public getGatewayInfo() {
    return {
      primary: this.config.gateway,
      fallback: this.config.fallbackGateway,
      hasFallback: !!this.fallbackGateway,
    };
  }
}
