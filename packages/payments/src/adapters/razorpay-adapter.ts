// @ts-nocheck TODO: fix this

import { Razorpay } from "@acme/razorpay-sdk";

import type {
  BillResponse,
  ContactResponse,
  CreateBillRequest,
  CreateContactRequest,
  CreateFundAccountRequest,
  CreateOrderRequest,
  CreatePayoutRequest,
  FundAccountResponse,
  IPaymentGateway,
  OrderResponse,
  PaymentResponse,
  PayoutResponse,
  ValidateFundAccountRequest,
  ValidateFundAccountResponse,
  VerifyPaymentRequest,
  VerifyPaymentResponse,
} from "../types";

export class RazorpayAdapter implements IPaymentGateway {
  private razorpay: Razorpay;

  constructor(config: { keyId: string; keySecret: string; baseURL?: string }) {
    this.razorpay = new Razorpay(config);
  }

  async createOrder(
    request: CreateOrderRequest,
  ): Promise<PaymentResponse<OrderResponse>> {
    try {
      // Convert amount to paise for Razorpay
      const razorpayRequest = {
        amount: request.amount * 100,
        currency: request.currency,
        receipt: request.receipt || `receipt_${Date.now()}`,
        notes: request.notes || {},
      };

      const response = await this.razorpay.orders.create(razorpayRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const orderResponse: OrderResponse = {
        id: response.data!.id,
        amount: response.data!.amount / 100, // Convert back to rupees
        currency: response.data!.currency,
        receipt: response.data!.receipt,
        status: response.data!.status,
        created_at: response.data!.created_at,
        notes: response.data!.notes,
      };

      return { data: orderResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async verifyPayment(
    request: VerifyPaymentRequest,
  ): Promise<PaymentResponse<VerifyPaymentResponse>> {
    try {
      // Razorpay verification logic using crypto
      const crypto = require("crypto");
      const generated_signature = crypto
        .createHmac("sha256", process.env.RAZORPAY_SECRET_KEY || "")
        .update(`${request.orderId}|${request.paymentId}`)
        .digest("hex");

      const verified = generated_signature === request.signature;

      const verifyResponse: VerifyPaymentResponse = {
        verified,
        paymentId: request.paymentId,
        orderId: request.orderId,
      };

      return { data: verifyResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createContact(
    request: CreateContactRequest,
  ): Promise<PaymentResponse<ContactResponse>> {
    const response = await this.razorpay.createContact(request);
    return response;
  }

  async createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    const response = await this.razorpay.createFundAccount(request);
    return response;
  }

  async getAllFundAccounts(
    contactId: string,
  ): Promise<PaymentResponse<FundAccountResponse[]>> {
    const response = await this.razorpay.getAllFundAccounts(contactId);
    return response;
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    const response = await this.razorpay.deactivateFundAccount(fundAccountId);
    return response;
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    const response = await this.razorpay.reactivateFundAccount(fundAccountId);
    return response;
  }

  async createPayout(
    request: CreatePayoutRequest,
  ): Promise<PaymentResponse<PayoutResponse>> {
    const response = await this.razorpay.createPayout(request);
    return response;
  }

  async createBill(
    request: CreateBillRequest,
  ): Promise<PaymentResponse<BillResponse>> {
    const response = await this.razorpay.createBill(request);
    return response;
  }

  async validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PaymentResponse<ValidateFundAccountResponse>> {
    const response = await this.razorpay.validateFundAccount(request);
    return response;
  }
}
