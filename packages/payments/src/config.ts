import type { PaymentConfig, PaymentGateway } from "./types";

export interface PaymentEnvironmentConfig {
  // Primary gateway configuration
  PAYMENT_GATEWAY_PRIMARY?: PaymentGateway;
  PAYMENT_GATEWAY_FALLBACK?: PaymentGateway;
  
  // Razorpay configuration
  RAZORPAY_KEY_ID?: string;
  RAZORPAY_SECRET_KEY?: string;
  RAZORPAY_BASE_URL?: string;
  
  // Cashfree configuration
  CASHFREE_APP_ID?: string;
  CASHFREE_SECRET_KEY?: string;
  CASHFREE_ENVIRONMENT?: "sandbox" | "production";
  
  // Feature flags
  ENABLE_PAYMENT_GATEWAY_FALLBACK?: string;
  ENABLE_PAYMENT_GATEWAY_ANALYTICS?: string;
}

export class PaymentConfigManager {
  private static instance: PaymentConfigManager;
  private config: PaymentConfig;

  private constructor(envConfig?: PaymentEnvironmentConfig) {
    this.config = this.buildConfigFromEnvironment(envConfig);
  }

  public static getInstance(envConfig?: PaymentEnvironmentConfig): PaymentConfigManager {
    if (!PaymentConfigManager.instance) {
      PaymentConfigManager.instance = new PaymentConfigManager(envConfig);
    }
    return PaymentConfigManager.instance;
  }

  public static updateConfig(envConfig: PaymentEnvironmentConfig): void {
    if (PaymentConfigManager.instance) {
      PaymentConfigManager.instance.config = PaymentConfigManager.instance.buildConfigFromEnvironment(envConfig);
    }
  }

  private buildConfigFromEnvironment(envConfig?: PaymentEnvironmentConfig): PaymentConfig {
    const env = envConfig || process.env;

    // Determine primary gateway
    const primaryGateway: PaymentGateway = (env.PAYMENT_GATEWAY_PRIMARY as PaymentGateway) || "RAZORPAY";
    
    // Determine fallback gateway
    const fallbackGateway: PaymentGateway | undefined = env.ENABLE_PAYMENT_GATEWAY_FALLBACK === "true" 
      ? (env.PAYMENT_GATEWAY_FALLBACK as PaymentGateway) || (primaryGateway === "RAZORPAY" ? "CASHFREE" : "RAZORPAY")
      : undefined;

    const config: PaymentConfig = {
      gateway: primaryGateway,
      fallbackGateway,
    };

    // Add Razorpay configuration if needed
    if (primaryGateway === "RAZORPAY" || fallbackGateway === "RAZORPAY") {
      if (!env.RAZORPAY_KEY_ID || !env.RAZORPAY_SECRET_KEY) {
        throw new Error("Razorpay configuration is incomplete. RAZORPAY_KEY_ID and RAZORPAY_SECRET_KEY are required.");
      }
      
      config.razorpay = {
        keyId: env.RAZORPAY_KEY_ID,
        keySecret: env.RAZORPAY_SECRET_KEY,
        baseURL: env.RAZORPAY_BASE_URL,
      };
    }

    // Add Cashfree configuration if needed
    if (primaryGateway === "CASHFREE" || fallbackGateway === "CASHFREE") {
      if (!env.CASHFREE_APP_ID || !env.CASHFREE_SECRET_KEY) {
        throw new Error("Cashfree configuration is incomplete. CASHFREE_APP_ID and CASHFREE_SECRET_KEY are required.");
      }
      
      config.cashfree = {
        appId: env.CASHFREE_APP_ID,
        secretKey: env.CASHFREE_SECRET_KEY,
        environment: (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
      };
    }

    return config;
  }

  public getConfig(): PaymentConfig {
    return { ...this.config };
  }

  public getPrimaryGateway(): PaymentGateway {
    return this.config.gateway;
  }

  public getFallbackGateway(): PaymentGateway | undefined {
    return this.config.fallbackGateway;
  }

  public hasFallback(): boolean {
    return !!this.config.fallbackGateway;
  }

  public isAnalyticsEnabled(): boolean {
    return process.env.ENABLE_PAYMENT_GATEWAY_ANALYTICS === "true";
  }

  public getRazorpayConfig() {
    return this.config.razorpay;
  }

  public getCashfreeConfig() {
    return this.config.cashfree;
  }

  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate primary gateway configuration
    if (this.config.gateway === "RAZORPAY" && !this.config.razorpay) {
      errors.push("Razorpay configuration is missing for primary gateway");
    }

    if (this.config.gateway === "CASHFREE" && !this.config.cashfree) {
      errors.push("Cashfree configuration is missing for primary gateway");
    }

    // Validate fallback gateway configuration
    if (this.config.fallbackGateway === "RAZORPAY" && !this.config.razorpay) {
      errors.push("Razorpay configuration is missing for fallback gateway");
    }

    if (this.config.fallbackGateway === "CASHFREE" && !this.config.cashfree) {
      errors.push("Cashfree configuration is missing for fallback gateway");
    }

    // Validate that primary and fallback are different
    if (this.config.fallbackGateway && this.config.gateway === this.config.fallbackGateway) {
      errors.push("Primary and fallback gateways cannot be the same");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  public getGatewayInfo() {
    return {
      primary: this.config.gateway,
      fallback: this.config.fallbackGateway,
      hasFallback: this.hasFallback(),
      analyticsEnabled: this.isAnalyticsEnabled(),
    };
  }
}

// Utility functions for easy configuration
export function createPaymentConfig(envConfig?: PaymentEnvironmentConfig): PaymentConfig {
  const manager = PaymentConfigManager.getInstance(envConfig);
  return manager.getConfig();
}

export function validatePaymentConfig(envConfig?: PaymentEnvironmentConfig): { isValid: boolean; errors: string[] } {
  const manager = PaymentConfigManager.getInstance(envConfig);
  return manager.validateConfig();
}

export function getPaymentGatewayInfo(envConfig?: PaymentEnvironmentConfig) {
  const manager = PaymentConfigManager.getInstance(envConfig);
  return manager.getGatewayInfo();
}
