// Common payment gateway types and interfaces

export type PaymentGateway = "RAZORPAY" | "CASHFREE";

export interface PaymentConfig {
  gateway: PaymentGateway;
  fallbackGateway?: PaymentGateway;
  razorpay?: {
    keyId: string;
    keySecret: string;
    baseURL?: string;
  };
  cashfree?: {
    appId: string;
    secretKey: string;
    environment: "sandbox" | "production";
  };
}

export interface PaymentResponse<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

// Order interfaces
export interface CreateOrderRequest {
  amount: number;
  currency: string;
  receipt?: string;
  notes?: {
    scraphub_id?: string;
    scraphub_employee_id?: string;
    transaction_for?: string;
    customer_name: string;
    customer_email: string;
    customer_phone: string;
    description: string;
  };
  customer_id: string;
}

export interface OrderResponse {
  id: string;
  amount: number;
  currency: string;
  receipt?: string;
  status: string;
  created_at: number;
  payment_session_id: string;
  notes?: Record<string, string>;
}

// Payment verification interfaces
export interface VerifyPaymentRequest {
  orderId: string;
  paymentId: string;
  signature: string;
}

export interface VerifyPaymentResponse {
  verified: boolean;
  paymentId: string;
  orderId: string;
}

// Contact interfaces
export interface CreateContactRequest {
  name: string;
  email?: string;
  contact?: string;
  type?: "customer" | "vendor" | "employee";
  reference_id?: string;
  notes?: Record<string, string>;
}

export interface ContactResponse {
  id: string;
  entity: string;
  name: string;
  contact?: string;
  email?: string;
  type: string;
  reference_id?: string;
  batch_id?: string;
  active: boolean;
  notes: Record<string, string>;
  created_at: number;
}

// Fund Account interfaces
export interface CreateFundAccountRequest {
  contact_id: string;
  account_type: "bank_account" | "vpa" | "card";
  bank_account?: {
    name: string;
    account_number: string;
    ifsc: string;
  };
  vpa?: {
    address: string;
  };
  card?: {
    name: string;
    number: string;
  };
}

export interface FundAccountResponse {
  id: string;
  entity: string;
  contact_id: string;
  account_type: string;
  bank_account?: {
    name: string;
    account_number: string;
    ifsc: string;
    bank_name: string;
  };
  vpa?: {
    address: string;
    handle: string;
  };
  card?: {
    name: string;
    last4: string;
    network: string;
    type: string;
    issuer: string;
  };
  batch_id?: string;
  active: boolean;
  created_at: number;
}

// Payout interfaces
export interface CreatePayoutRequest {
  beneficiary_id: string;
  fundsource_id: string;
  amount: number;
  currency: string;
  mode: "IMPS" | "NEFT" | "RTGS" | "UPI";
  purpose: string;
  queue_if_low_balance?: boolean;
  reference_id?: string;
  narration?: string;
  notes?: Record<string, string>;
}

export interface PayoutResponse {
  id: string;
  entity: string;
  fund_account_id: string;
  amount: number;
  currency: string;
  notes: Record<string, string>;
  fees: number;
  tax: number;
  status: string;
  purpose: string;
  utr?: string;
  mode: string;
  reference_id?: string;
  narration?: string;
  batch_id?: string;
  failure_reason?: string;
  created_at: number;
}

// Bill interfaces
export interface CreateBillRequest {
  business_category: string;
  business_type: string;
  receipt_delivery: string;
  receipt_number: string;
  receipt_summary: {
    currency: string;
    total_quantity: number;
    net_payable_amount: number;
    payment_status: string;
    total_tax_percent: number;
    total_tax_amount: number;
    sub_total_amount: number;
  };
  receipt_timestamp: number;
  receipt_type: string;
}

export interface BillResponse {
  id: string;
  entity: string;
  receipt_number: string;
  invoice_number: string;
  customer_id: string;
  customer_contact: string;
  customer_email: string;
  customer_name: string;
  order_id: string;
  invoice_id: string;
  type: string;
  group_taxes_discounts: boolean;
  paid: boolean;
  partially_paid: boolean;
  amount: number;
  amount_paid: number;
  amount_due: number;
  currency: string;
  description?: string;
  notes: Record<string, string>;
  comment?: string;
  short_url: string;
  view_less: boolean;
  billing_start: number;
  billing_end: number;
  issued_at: number;
  paid_at?: number;
  cancelled_at?: number;
  expired_at: number;
  sms_status?: string;
  email_status?: string;
  date: number;
  terms?: string;
  partial_payment: boolean;
  gross_amount: number;
  tax_amount: number;
  taxable_amount: number;
  amount_in_words: string;
  created_at: number;
  idempotency_key?: string;
}

// Validation interfaces
export interface ValidateFundAccountRequest {
  fund_account: {
    id: string;
  };
  amount: number;
  currency: string;
  notes?: Record<string, string>;
}

export interface ValidateFundAccountResponse {
  id: string;
  entity: string;
  fund_account: FundAccountResponse;
  status: string;
  amount: number;
  currency: string;
  notes: Record<string, string>;
  results: {
    account_status: string;
    registered_name: string;
  };
  created_at: number;
}

// Abstract payment gateway interface
export interface IPaymentGateway {
  // Order operations
  createOrder(
    request: CreateOrderRequest,
  ): Promise<PaymentResponse<OrderResponse>>;
  verifyPayment(
    request: VerifyPaymentRequest,
  ): Promise<PaymentResponse<VerifyPaymentResponse>>;

  // Contact operations
  createContact(
    request: CreateContactRequest,
  ): Promise<PaymentResponse<ContactResponse>>;

  // Fund account operations
  createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PaymentResponse<FundAccountResponse>>;
  getAllFundAccounts(
    contactId: string,
  ): Promise<PaymentResponse<FundAccountResponse[]>>;
  deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>>;
  reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>>;

  // Payout operations
  createPayout(
    request: CreatePayoutRequest,
  ): Promise<PaymentResponse<PayoutResponse>>;

  // Bill operations
  createBill(
    request: CreateBillRequest,
  ): Promise<PaymentResponse<BillResponse>>;

  // Validation operations
  validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PaymentResponse<ValidateFundAccountResponse>>;
}
