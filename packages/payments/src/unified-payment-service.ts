import { Analytics } from "@acme/analytics";

import type {
  BillResponse,
  ContactResponse,
  CreateBillRequest,
  CreateContactRequest,
  CreateFundAccountRequest,
  CreateOrderRequest,
  CreatePayoutRequest,
  FundAccountResponse,
  OrderResponse,
  PaymentConfig,
  PaymentResponse,
  PayoutResponse,
  ValidateFundAccountRequest,
  ValidateFundAccountResponse,
  VerifyPaymentRequest,
  VerifyPaymentResponse,
} from "./types";
import { PaymentFactory } from "./payment-factory";

export class UnifiedPaymentService {
  private factory: PaymentFactory;
  private analytics?: Analytics;

  constructor(config: PaymentConfig, analytics?: Analytics) {
    this.factory = PaymentFactory.getInstance(config);
    this.analytics = analytics;
  }

  public static updateConfig(config: PaymentConfig): void {
    PaymentFactory.updateConfig(config);
  }

  // Order operations
  async createOrder(
    request: CreateOrderRequest,
  ): Promise<PaymentResponse<OrderResponse>> {
    const startTime = Date.now();
    const gateway = this.factory.getCurrentGateway();

    // Track payment initiation
    this.analytics?.trackPaymentInitiated({
      gateway,
      amount: request.amount,
      currency: request.currency,
      order_id: request.receipt || "unknown",
    });

    try {
      const result = await this.factory.executeWithFallback(
        (gateway) => gateway.createOrder(request),
        "createOrder",
      );

      const processingTime = Date.now() - startTime;

      // Track gateway performance
      this.analytics?.trackGatewayResponse({
        gateway,
        operation: "createOrder",
        success: !result.error,
        response_time_ms: processingTime,
        error_message: result.error?.message,
      });

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;

      this.analytics?.trackGatewayResponse({
        gateway,
        operation: "createOrder",
        success: false,
        response_time_ms: processingTime,
        error_message: (error as Error).message,
      });

      throw error;
    }
  }

  async verifyPayment(
    request: VerifyPaymentRequest,
  ): Promise<PaymentResponse<VerifyPaymentResponse>> {
    const startTime = Date.now();
    const gateway = this.factory.getCurrentGateway();

    try {
      const result = await this.factory.executeWithFallback(
        (gateway) => gateway.verifyPayment(request),
        "verifyPayment",
      );

      const processingTime = Date.now() - startTime;

      if (result.data?.verified) {
        // Track successful payment
        this.analytics?.trackPaymentSuccess({
          gateway,
          amount: 0, // Amount not available in verify request
          currency: "INR",
          order_id: request.orderId,
          transaction_id: request.paymentId,
          processing_time_ms: processingTime,
        });
      } else {
        // Track failed payment verification
        this.analytics?.trackPaymentFailed({
          gateway,
          amount: 0,
          currency: "INR",
          order_id: request.orderId,
          failure_reason: "Payment verification failed",
          processing_time_ms: processingTime,
        });
      }

      return result;
    } catch (error) {
      const processingTime = Date.now() - startTime;

      this.analytics?.trackPaymentFailed({
        gateway,
        amount: 0,
        currency: "INR",
        order_id: request.orderId,
        failure_reason: (error as Error).message,
        processing_time_ms: processingTime,
      });

      throw error;
    }
  }

  // Contact operations
  async createContact(
    request: CreateContactRequest,
  ): Promise<PaymentResponse<ContactResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createContact(request),
      "createContact",
    );
  }

  // Fund account operations
  async createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createFundAccount(request),
      "createFundAccount",
    );
  }

  async getAllFundAccounts(
    contactId: string,
  ): Promise<PaymentResponse<FundAccountResponse[]>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.getAllFundAccounts(contactId),
      "getAllFundAccounts",
    );
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.deactivateFundAccount(fundAccountId),
      "deactivateFundAccount",
    );
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PaymentResponse<FundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.reactivateFundAccount(fundAccountId),
      "reactivateFundAccount",
    );
  }

  // Payout operations
  async createPayout(
    request: CreatePayoutRequest,
  ): Promise<PaymentResponse<PayoutResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createPayout(request),
      "createPayout",
    );
  }

  // Bill operations
  async createBill(
    request: CreateBillRequest,
  ): Promise<PaymentResponse<BillResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createBill(request),
      "createBill",
    );
  }

  // Validation operations
  async validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PaymentResponse<ValidateFundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.validateFundAccount(request),
      "validateFundAccount",
    );
  }

  // Utility methods
  getCurrentGateway() {
    return this.factory.getCurrentGateway();
  }

  getFallbackGateway() {
    return this.factory.getFallbackGatewayType();
  }

  getGatewayInfo() {
    return this.factory.getGatewayInfo();
  }

  // Gateway-specific operations (when you need to use a specific gateway)
  async executeWithSpecificGateway<T>(
    gatewayType: "primary" | "fallback",
    operation: (gateway: any) => Promise<T>,
  ): Promise<T> {
    const gateway =
      gatewayType === "primary"
        ? this.factory.getPrimaryGateway()
        : this.factory.getFallbackGateway();

    if (!gateway) {
      throw new Error(`${gatewayType} gateway is not available`);
    }

    return operation(gateway);
  }
}
