import { Cashfree } from "@acme/cashfree-sdk";
import { Razorpay } from "@acme/razorpay-sdk";

import type { PaymentEnvironmentConfig } from "./config";

export interface CustomerData {
  customerId: string;
  name?: string;
  email?: string;
  phone?: string;
  bankAccount?: string;
  ifsc?: string;
}

export interface CustomerCreationResult {
  success: boolean;
  customerId: string;
  gateway: "RAZORPAY" | "CASHFREE";
  error?: string;
}

export class CustomerManager {
  private cashfree?: Cashfree;
  private razorpay?: Razorpay;

  constructor(config: PaymentEnvironmentConfig) {
    // Initialize Cashfree if configured
    if (config.CASHFREE_APP_ID && config.CASHFREE_SECRET_KEY) {
      this.cashfree = new Cashfree({
        appId: config.CASHFREE_APP_ID,
        secretKey: config.CASHFREE_SECRET_KEY,
        environment: config.CASHFREE_ENVIRONMENT || "sandbox",
      });
      console.log("Cashfree initialized", this.cashfree);
    }

    // Initialize Razorpay if configured
    if (config.RAZORPAY_KEY_ID && config.RAZORPAY_SECRET_KEY) {
      this.razorpay = new Razorpay({
        keyId: config.RAZORPAY_KEY_ID,
        keySecret: config.RAZORPAY_SECRET_KEY,
      });
    }
  }

  /**
   * Create or get customer for the specified gateway
   */
  async ensureCustomer(
    customerData: CustomerData,
    gateway: "RAZORPAY" | "CASHFREE",
  ): Promise<CustomerCreationResult> {
    try {
      if (gateway === "CASHFREE" && this.cashfree) {
        return await this.ensureCashfreeCustomer(customerData);
      } else if (gateway === "RAZORPAY" && this.razorpay) {
        return await this.ensureRazorpayCustomer(customerData);
      } else {
        return {
          success: false,
          customerId: customerData.customerId,
          gateway,
          error: `${gateway} not configured or not available`,
        };
      }
    } catch (error) {
      return {
        success: false,
        customerId: customerData.customerId,
        gateway,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Ensure Cashfree customer exists
   */
  private async ensureCashfreeCustomer(
    customerData: CustomerData,
  ): Promise<CustomerCreationResult> {
    if (!this.cashfree) {
      throw new Error("Cashfree not configured");
    }

    // Create new customer
    const createResult = await this.cashfree.createCustomer({
      customer_name: customerData.name,
      customer_email: customerData.email,
      customer_phone: customerData.phone,
    });

    if (createResult.error) {
      throw createResult.error;
    }

    return {
      success: true,
      customerId: customerData.customerId,
      gateway: "CASHFREE",
    };
  }

  /**
   * Ensure Razorpay customer exists
   * Note: Razorpay customer management is not implemented yet
   */
  private async ensureRazorpayCustomer(
    customerData: CustomerData,
  ): Promise<CustomerCreationResult> {
    if (!this.razorpay) {
      throw new Error("Razorpay not configured");
    }

    // For now, Razorpay doesn't require pre-created customers
    // Orders can be created with customer details directly
    return {
      success: true,
      customerId: customerData.customerId,
      gateway: "RAZORPAY",
    };
  }

  /**
   * Create customers for both gateways (useful for backup)
   */
  async ensureCustomerBothGateways(customerData: CustomerData): Promise<{
    cashfree?: CustomerCreationResult;
    razorpay?: CustomerCreationResult;
  }> {
    const results: {
      cashfree?: CustomerCreationResult;
      razorpay?: CustomerCreationResult;
    } = {};

    // Create Cashfree customer if available
    if (this.cashfree) {
      try {
        results.cashfree = await this.ensureCashfreeCustomer(customerData);
      } catch (error) {
        results.cashfree = {
          success: false,
          customerId: customerData.customerId,
          gateway: "CASHFREE",
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    }

    // Create Razorpay customer if available
    if (this.razorpay) {
      try {
        results.razorpay = await this.ensureRazorpayCustomer(customerData);
      } catch (error) {
        results.razorpay = {
          success: false,
          customerId: customerData.customerId,
          gateway: "RAZORPAY",
          error: error instanceof Error ? error.message : "Unknown error",
        };
      }
    }

    return results;
  }
}
