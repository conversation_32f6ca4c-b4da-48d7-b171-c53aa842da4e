# Unified Payments Package

A comprehensive payment gateway abstraction layer supporting both Razorpay and Cashfree with automatic fallback capabilities.

## Features

- **Multi-Gateway Support**: Seamlessly switch between Razorpay and Cashfree
- **Automatic Fallback**: If primary gateway fails, automatically retry with fallback gateway
- **Unified API**: Single interface for all payment operations regardless of gateway
- **Type Safety**: Full TypeScript support with comprehensive type definitions
- **Configuration Management**: Environment-based configuration with validation
- **Error Handling**: Robust error handling with detailed error messages
- **Logging**: Built-in logging for debugging and monitoring

## Installation

```bash
# Install the unified payments package
npm install @acme/payments

# Install gateway-specific SDKs (automatically included as dependencies)
# @acme/razorpay-sdk
# @acme/cashfree-sdk
```

## Quick Start

### 1. Environment Configuration

```bash
# Primary gateway
PAYMENT_GATEWAY_PRIMARY="RAZORPAY"  # or "CASHFREE"

# Optional fallback gateway
PAYMENT_GATEWAY_FALLBACK="CASHFREE"  # or "RAZORPAY"
ENABLE_PAYMENT_GATEWAY_FALLBACK="true"

# Razorpay credentials
RAZORPAY_KEY_ID="rzp_test_your_key_id"
RAZORPAY_SECRET_KEY="your_razorpay_secret_key"

# Cashfree credentials
CASHFREE_APP_ID="your_cashfree_app_id"
CASHFREE_SECRET_KEY="your_cashfree_secret_key"
CASHFREE_ENVIRONMENT="sandbox"  # or "production"
```

### 2. Basic Usage

```typescript
import { UnifiedPaymentService, createPaymentConfig } from "@acme/payments";

// Create configuration
const config = createPaymentConfig();

// Initialize service
const paymentService = new UnifiedPaymentService(config);

// Create an order
const orderResult = await paymentService.createOrder({
  amount: 100, // Amount in rupees
  currency: "INR",
  receipt: "order_123",
  notes: {
    customer_id: "customer_123",
    description: "Test payment"
  }
});

if (orderResult.data) {
  console.log("Order created:", orderResult.data.id);
}
```

### 3. React Native Integration

```typescript
import { useUnifiedPayment } from "@/components/shared/payments/unified-payment-handler";

function PaymentComponent() {
  const { processPayment, getCurrentGateway } = useUnifiedPayment();

  const handlePayment = async () => {
    const result = await processPayment({
      orderId: "order_123",
      amount: 100,
      currency: "INR",
      name: "Test Payment",
      description: "Payment for services",
      customerEmail: "<EMAIL>",
      customerPhone: "+919876543210",
    });

    if (result.success) {
      console.log("Payment successful:", result.paymentId);
    } else {
      console.error("Payment failed:", result.error);
    }
  };

  return (
    <button onClick={handlePayment}>
      Pay with {getCurrentGateway()}
    </button>
  );
}
```

## API Reference

### UnifiedPaymentService

#### Order Operations
- `createOrder(request)` - Create a payment order
- `verifyPayment(request)` - Verify payment completion

#### Contact Operations
- `createContact(request)` - Create a contact/customer
- `createFundAccount(request)` - Create fund account for payouts
- `getAllFundAccounts(contactId)` - Get all fund accounts for a contact

#### Payout Operations
- `createPayout(request)` - Create a payout/transfer
- `validateFundAccount(request)` - Validate fund account details

#### Utility Methods
- `getCurrentGateway()` - Get current primary gateway
- `getFallbackGateway()` - Get fallback gateway (if configured)
- `getGatewayInfo()` - Get comprehensive gateway information

### Configuration Management

```typescript
import { PaymentConfigManager, validatePaymentConfig } from "@acme/payments";

// Validate configuration
const validation = validatePaymentConfig();
if (!validation.isValid) {
  console.error("Configuration errors:", validation.errors);
}

// Get gateway information
const info = PaymentConfigManager.getInstance().getGatewayInfo();
console.log("Primary gateway:", info.primary);
console.log("Has fallback:", info.hasFallback);
```

## Gateway-Specific Features

### Razorpay
- Full support for all Razorpay APIs
- Orders, Payments, Contacts, Fund Accounts, Payouts
- Webhook verification
- Signature validation

### Cashfree
- Orders and Payments API
- Beneficiaries (equivalent to contacts)
- Transfers (equivalent to payouts)
- Webhook verification
- Payment status verification

## Error Handling

The package provides comprehensive error handling with automatic fallback:

```typescript
try {
  const result = await paymentService.createOrder(orderRequest);
  // Handle success
} catch (error) {
  // Error contains details about both primary and fallback failures
  console.error("Payment operation failed:", error.message);
}
```

## Database Schema

The package expects the following database schema updates:

```sql
-- Add payment gateway enum
CREATE TYPE payment_gateway_enum AS ENUM ('RAZORPAY', 'CASHFREE');

-- Update payment transaction tables
ALTER TABLE kabadiwala_payment_transaction 
ADD COLUMN payment_gateway payment_gateway_enum NOT NULL DEFAULT 'RAZORPAY',
ADD COLUMN cashfree_order_id TEXT UNIQUE,
ADD COLUMN cashfree_payment_id TEXT UNIQUE,
ADD COLUMN gateway_metadata JSONB;

ALTER TABLE customer_payment_transaction 
ADD COLUMN payment_gateway payment_gateway_enum NOT NULL DEFAULT 'RAZORPAY',
ADD COLUMN cashfree_order_id TEXT UNIQUE,
ADD COLUMN cashfree_payment_id TEXT UNIQUE,
ADD COLUMN gateway_metadata JSONB;
```

## Webhook Integration

### Razorpay Webhooks
```typescript
// In your webhook handler
import { webhooksRouter } from "@acme/kabadiwala-api";

// The webhook router automatically handles Razorpay events
// POST /api/webhooks/razorpay
```

### Cashfree Webhooks
```typescript
// In your webhook handler
import { webhooksRouter } from "@acme/kabadiwala-api";

// The webhook router automatically handles Cashfree events
// POST /api/webhooks/cashfree
```

## Testing

### Development Setup
1. Set `CASHFREE_ENVIRONMENT="sandbox"` for testing
2. Use Razorpay test keys (`rzp_test_*`)
3. Enable fallback to test both gateways

### Production Deployment
1. Set `CASHFREE_ENVIRONMENT="production"`
2. Use Razorpay live keys (`rzp_live_*`)
3. Configure appropriate fallback strategy

## Migration from Razorpay-only

1. **Install packages**: Add `@acme/payments` to your dependencies
2. **Update imports**: Replace direct Razorpay imports with unified service
3. **Update environment**: Add Cashfree configuration variables
4. **Database migration**: Run schema updates for multi-gateway support
5. **Test thoroughly**: Verify both gateways work in your environment

## Best Practices

1. **Always use fallback**: Configure a fallback gateway for redundancy
2. **Monitor both gateways**: Track success rates and performance
3. **Handle errors gracefully**: Provide clear error messages to users
4. **Validate configuration**: Check configuration on startup
5. **Log operations**: Enable logging for debugging and monitoring
6. **Test webhooks**: Ensure webhook handlers work for both gateways

## Support

For issues and questions:
- Check the package documentation
- Review error logs for detailed error messages
- Verify environment configuration
- Test with both gateways individually
