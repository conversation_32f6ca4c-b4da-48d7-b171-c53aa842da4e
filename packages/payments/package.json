{"name": "@acme/payments", "version": "1.0.0", "description": "Unified payment gateway SDK supporting Razorpay and Cashfree", "main": "src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "prepare": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["payments", "razorpay", "cashfree", "api", "typescript", "sdk"], "author": "", "license": "ISC", "dependencies": {"@acme/analytics": "workspace:*", "@acme/cashfree-sdk": "workspace:*", "@acme/razorpay-sdk": "workspace:*", "axios": "^1.9.0"}, "peerDependencies": {"typescript": "catalog:"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "catalog:"}}