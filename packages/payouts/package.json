{"name": "@acme/payouts", "version": "1.0.0", "description": "Unified payout management SDK supporting Razorpay and Cashfree with Cashfree as primary", "main": "src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "prepare": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["payouts", "razorpay", "cashfree", "api", "typescript", "sdk"], "author": "", "license": "ISC", "dependencies": {"@acme/cashfree-sdk": "workspace:*", "@acme/razorpay-sdk": "workspace:*", "axios": "^1.9.0"}, "peerDependencies": {"typescript": "catalog:"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "catalog:"}}