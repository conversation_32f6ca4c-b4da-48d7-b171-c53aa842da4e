import { Razorpay } from "@acme/razorpay-sdk";

import type {
  BulkPayoutRequest,
  BulkPayoutResponse,
  ContactResponse,
  CreateContactRequest,
  CreateFundAccountRequest,
  CreatePayoutRequest,
  FundAccountResponse,
  IPayoutGateway,
  PayoutResponseContainer,
  PayoutResponse as PayoutResponseType,
  PayoutStatusResponse,
  ValidateFundAccountRequest,
  ValidateFundAccountResponse,
} from "../types";

export class RazorpayPayoutAdapter implements IPayoutGateway {
  private razorpay: Razorpay;

  constructor(config: { keyId: string; keySecret: string; baseURL?: string }) {
    this.razorpay = new Razorpay(config);
  }

  async createContact(
    request: CreateContactRequest,
  ): Promise<PayoutResponseContainer<ContactResponse>> {
    try {
      // @ts-expect-error - TODO: fix this
      const response = await this.razorpay.createContact(request);
      // @ts-expect-error - TODO: fix this
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    try {
      // @ts-expect-error - TODO: fix this
      const response = await this.razorpay.createFundAccount(request);
      // @ts-expect-error - TODO: fix this
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getAllFundAccounts(
    contactId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse[]>> {
    try {
      // @ts-expect-error - TODO: fix this
      const response = await this.razorpay.getAllFundAccounts(contactId);
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    try {
      const response = await this.razorpay.deactivateFundAccount(fundAccountId);
      // @ts-expect-error - TODO: fix this
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    try {
      const response = await this.razorpay.reactivateFundAccount(fundAccountId);
      // @ts-expect-error - TODO: fix this
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createPayout(
    request: CreatePayoutRequest,
  ): Promise<PayoutResponseContainer<PayoutResponseType>> {
    try {
      // @ts-expect-error - TODO: fix this
      const response = await this.razorpay.createPayout(request);
      // @ts-expect-error - TODO: fix this
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getPayoutStatus(
    payoutId: string,
  ): Promise<PayoutResponseContainer<PayoutStatusResponse>> {
    try {
      // Razorpay doesn't have a specific get payout status method in our SDK
      // We would need to implement this by calling the Razorpay API directly
      // For now, return a mock response
      return {
        data: {
          id: payoutId,
          status: "processing",
          amount: 0,
          currency: "INR",
        },
        isLoading: false,
        error: null,
      };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createBulkPayout(
    request: BulkPayoutRequest,
  ): Promise<PayoutResponseContainer<BulkPayoutResponse>> {
    try {
      // Razorpay bulk payout would need to be implemented
      // For now, process payouts individually
      const results = await Promise.allSettled(
        request.payouts.map((payout) => this.createPayout(payout)),
      );

      const successful = results.filter(
        (r) => r.status === "fulfilled" && r.value.data,
      ).length;
      const failed = results.filter(
        (r) => r.status === "rejected" || !r.value.data,
      ).length;

      return {
        data: {
          id: `bulk_${Date.now()}`,
          entity: "bulk_payout",
          status: "completed",
          reference_id: request.reference_id,
          total_payouts: request.payouts.length,
          successful_payouts: successful,
          failed_payouts: failed,
          pending_payouts: 0,
          created_at: Math.floor(Date.now() / 1000),
        },
        isLoading: false,
        error: null,
      };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PayoutResponseContainer<ValidateFundAccountResponse>> {
    try {
      // @ts-expect-error - TODO: fix this
      const response = await this.razorpay.validateFundAccount(request);
      // @ts-expect-error - TODO: fix this
      return response;
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }
}
