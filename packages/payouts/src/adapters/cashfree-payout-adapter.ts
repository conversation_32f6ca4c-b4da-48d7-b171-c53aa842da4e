import { Cashfree, CashfreeCreateTransferRequest } from "@acme/cashfree-sdk";

import type {
  BulkPayoutRequest,
  BulkPayoutResponse,
  ContactResponse,
  CreateContactRequest,
  CreateFundAccountRequest,
  CreatePayoutRequest,
  FundAccountResponse,
  IPayoutGateway,
  PayoutResponseContainer,
  PayoutResponse as PayoutResponseType,
  PayoutStatusResponse,
  ValidateFundAccountRequest,
  ValidateFundAccountResponse,
} from "../types";

export class CashfreePayoutAdapter implements IPayoutGateway {
  private cashfree: Cashfree;

  constructor(config: {
    appId: string;
    secretKey: string;
    environment: "sandbox" | "production";
  }) {
    this.cashfree = new Cashfree(config);
  }

  async createContact(
    request: CreateContactRequest,
  ): Promise<PayoutResponseContainer<ContactResponse>> {
    try {
      const cashfreeRequest = {
        bene_id: request.reference_id || `contact_${Date.now()}`,
        name: request.name,
        email: request.email,
        phone: request.contact,
      };

      const response = await this.cashfree.createBeneficiary(cashfreeRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const contactResponse: ContactResponse = {
        id: response.data!.bene_id,
        entity: "contact",
        name: response.data!.name,
        contact: response.data!.phone,
        email: response.data!.email,
        type: request.type || "customer",
        reference_id: response.data!.bene_id,
        active: response.data!.status === "ACTIVE",
        notes: request.notes || {},
        created_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: contactResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    try {
      // For Cashfree, beneficiary creation includes fund account details
      const cashfreeRequest = {
        bene_id: `${request.contact_id}_${Date.now()}`,
        name: request.bank_account?.name || request.vpa?.address || "Unknown",
        email: "",
        phone: "",
        ...(request.bank_account && {
          bank_account: request.bank_account.account_number,
          ifsc: request.bank_account.ifsc,
        }),
        ...(request.vpa && {
          vpa: request.vpa.address,
        }),
      };

      const response = await this.cashfree.createBeneficiary(cashfreeRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const fundAccountResponse: FundAccountResponse = {
        id: response.data!.bene_id,
        entity: "fund_account",
        contact_id: request.contact_id,
        account_type: request.account_type,
        ...(request.bank_account && {
          bank_account: {
            name: response.data!.name,
            account_number: response.data!.bank_account || "",
            ifsc: response.data!.ifsc || "",
            bank_name: "",
          },
        }),
        ...(request.vpa && {
          vpa: {
            address: response.data!.vpa || "",
            handle: "",
          },
        }),
        active: response.data!.status === "ACTIVE",
        created_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: fundAccountResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getAllFundAccounts(
    contactId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse[]>> {
    // Cashfree doesn't have a direct equivalent, return empty array
    return { data: [], isLoading: false, error: null };
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    try {
      const response = await this.cashfree.removeBeneficiary(fundAccountId);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      // Return a mock response since Cashfree doesn't return the updated beneficiary
      const fundAccountResponse: FundAccountResponse = {
        id: fundAccountId,
        entity: "fund_account",
        contact_id: "",
        account_type: "bank_account",
        active: false,
        created_at: Date.now() / 1000,
      };

      return { data: fundAccountResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    // Cashfree doesn't support reactivation, would need to recreate
    return {
      data: null,
      isLoading: false,
      error: new Error("Cashfree doesn't support fund account reactivation"),
    };
  }

  async createPayout(
    request: CreatePayoutRequest,
  ): Promise<PayoutResponseContainer<PayoutResponseType>> {
    try {
      const transferMode: CashfreeCreateTransferRequest["transfer_mode"] =
        request.mode === "UPI" ? "upi" : "banktransfer";
      const cashfreeRequest = {
        bene_id: request.beneficiary_id,
        fundsource_id: request.fundsource_id,
        amount: request.amount,
        transfer_id: request.reference_id || `transfer_${Date.now()}`,
        transfer_mode: transferMode,
        remarks: request.narration || request.purpose,
      };

      const response = await this.cashfree.createTransfer(cashfreeRequest);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const payoutResponse: PayoutResponseType = {
        id: response.data!.cf_transfer_id,
        entity: "payout",
        fund_account_id: response.data!.beneficiary_details.beneficiary_id,
        amount: response.data!.transfer_amount,
        currency: request.currency,
        notes: request.notes || {},
        fees: 0, // Cashfree doesn't provide fees in response
        tax: 0,
        status: response.data!.status,
        purpose: request.purpose,
        utr: response.data!.transfer_utr,
        mode: response.data!.transfer_mode,
        reference_id: response.data!.transfer_id,
        // narration: response.data!.remarks,
        created_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: payoutResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getPayoutStatus(
    payoutId: string,
  ): Promise<PayoutResponseContainer<PayoutStatusResponse>> {
    try {
      const response = await this.cashfree.getTransferStatus(payoutId);

      if (response.error) {
        return { data: null, isLoading: false, error: response.error };
      }

      const statusResponse: PayoutStatusResponse = {
        id: response.data!.cf_transfer_id,
        status: response.data!.status,
        amount: response.data!.transfer_amount,
        currency: "INR",
        utr: response.data!.transfer_utr,
        processed_at: new Date(response.data!.added_on).getTime() / 1000,
      };

      return { data: statusResponse, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async createBulkPayout(
    request: BulkPayoutRequest,
  ): Promise<PayoutResponseContainer<BulkPayoutResponse>> {
    try {
      // Process payouts individually since Cashfree doesn't have bulk API
      const results = await Promise.allSettled(
        request.payouts.map((payout) => this.createPayout(payout)),
      );

      const successful = results.filter(
        (r) => r.status === "fulfilled" && r.value.data,
      ).length;
      const failed = results.filter(
        (r) => r.status === "rejected" || !r.value.data,
      ).length;

      return {
        data: {
          id: `bulk_${Date.now()}`,
          entity: "bulk_payout",
          status: "completed",
          reference_id: request.reference_id,
          total_payouts: request.payouts.length,
          successful_payouts: successful,
          failed_payouts: failed,
          pending_payouts: 0,
          created_at: Math.floor(Date.now() / 1000),
        },
        isLoading: false,
        error: null,
      };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PayoutResponseContainer<ValidateFundAccountResponse>> {
    // Cashfree doesn't have fund account validation like Razorpay
    return {
      data: null,
      isLoading: false,
      error: new Error("Cashfree doesn't support fund account validation"),
    };
  }
}
