import type { IPayoutGateway, PayoutConfig, PayoutGateway } from "./types";
import { RazorpayPayoutAdapter } from "./adapters/razorpay-payout-adapter";
import { CashfreePayoutAdapter } from "./adapters/cashfree-payout-adapter";

export class PayoutFactory {
  private static instance: PayoutFactory;
  private config: PayoutConfig;
  private primaryGateway: IPayoutGateway | null = null;
  private fallbackGateway: IPayoutGateway | null = null;

  private constructor(config: PayoutConfig) {
    this.config = config;
    this.initializeGateways();
  }

  public static getInstance(config?: PayoutConfig): PayoutFactory {
    if (!PayoutFactory.instance) {
      if (!config) {
        throw new Error("PayoutFactory must be initialized with config on first use");
      }
      PayoutFactory.instance = new PayoutFactory(config);
    }
    return PayoutFactory.instance;
  }

  public static updateConfig(config: PayoutConfig): void {
    if (PayoutFactory.instance) {
      PayoutFactory.instance.config = config;
      PayoutFactory.instance.initializeGateways();
    }
  }

  private initializeGateways(): void {
    // Initialize primary gateway
    this.primaryGateway = this.createGateway(this.config.primaryGateway);

    // Initialize fallback gateway if specified
    if (this.config.fallbackGateway && this.config.fallbackGateway !== this.config.primaryGateway) {
      this.fallbackGateway = this.createGateway(this.config.fallbackGateway);
    }
  }

  private createGateway(gateway: PayoutGateway): IPayoutGateway {
    switch (gateway) {
      case "RAZORPAY":
        if (!this.config.razorpay) {
          throw new Error("Razorpay configuration is required");
        }
        return new RazorpayPayoutAdapter(this.config.razorpay);

      case "CASHFREE":
        if (!this.config.cashfree) {
          throw new Error("Cashfree configuration is required");
        }
        return new CashfreePayoutAdapter(this.config.cashfree);

      default:
        throw new Error(`Unsupported payout gateway: ${gateway}`);
    }
  }

  public getPrimaryGateway(): IPayoutGateway {
    if (!this.primaryGateway) {
      throw new Error("Primary payout gateway not initialized");
    }
    return this.primaryGateway;
  }

  public getFallbackGateway(): IPayoutGateway | null {
    return this.fallbackGateway;
  }

  public getCurrentGateway(): PayoutGateway {
    return this.config.primaryGateway;
  }

  public getFallbackGatewayType(): PayoutGateway | undefined {
    return this.config.fallbackGateway;
  }

  public async executeWithFallback<T>(
    operation: (gateway: IPayoutGateway) => Promise<T>,
    operationName: string = "payout operation"
  ): Promise<T> {
    try {
      console.log(`Executing ${operationName} with primary gateway: ${this.config.primaryGateway}`);
      const result = await operation(this.getPrimaryGateway());
      
      // Check if the result indicates an error (for PayoutResponse types)
      if (typeof result === 'object' && result !== null && 'error' in result && result.error) {
        throw result.error;
      }
      
      return result;
    } catch (primaryError) {
      console.error(`Primary gateway (${this.config.primaryGateway}) failed for ${operationName}:`, primaryError);
      
      if (this.fallbackGateway && this.config.fallbackGateway) {
        try {
          console.log(`Attempting ${operationName} with fallback gateway: ${this.config.fallbackGateway}`);
          const result = await operation(this.fallbackGateway);
          
          // Check if the result indicates an error (for PayoutResponse types)
          if (typeof result === 'object' && result !== null && 'error' in result && result.error) {
            throw result.error;
          }
          
          console.log(`Fallback gateway (${this.config.fallbackGateway}) succeeded for ${operationName}`);
          return result;
        } catch (fallbackError) {
          console.error(`Fallback gateway (${this.config.fallbackGateway}) also failed for ${operationName}:`, fallbackError);
          throw new Error(`Both primary (${this.config.primaryGateway}) and fallback (${this.config.fallbackGateway}) gateways failed. Primary error: ${primaryError}. Fallback error: ${fallbackError}`);
        }
      }
      
      throw primaryError;
    }
  }

  public getGatewayInfo() {
    return {
      primary: this.config.primaryGateway,
      fallback: this.config.fallbackGateway,
      hasFallback: !!this.fallbackGateway,
    };
  }
}
