import type { PayoutConfig, PayoutGateway } from "./types";

export interface PayoutEnvironmentConfig {
  // Primary gateway configuration (Cashfree is primary for payouts)
  PAYOUT_GATEWAY_PRIMARY?: PayoutGateway;
  PAYOUT_GATEWAY_FALLBACK?: PayoutGateway;
  
  // Razorpay configuration
  RAZORPAY_KEY_ID?: string;
  RAZORPAY_SECRET_KEY?: string;
  RAZORPAY_BASE_URL?: string;
  
  // Cashfree configuration
  CASHFREE_APP_ID?: string;
  CASHFREE_SECRET_KEY?: string;
  CASHFREE_ENVIRONMENT?: "sandbox" | "production";
  
  // Feature flags
  ENABLE_PAYOUT_GATEWAY_FALLBACK?: string;
  ENABLE_PAYOUT_ANALYTICS?: string;
}

export class PayoutConfigManager {
  private static instance: PayoutConfigManager;
  private config: PayoutConfig;

  private constructor(envConfig?: PayoutEnvironmentConfig) {
    this.config = this.buildConfigFromEnvironment(envConfig);
  }

  public static getInstance(envConfig?: PayoutEnvironmentConfig): PayoutConfigManager {
    if (!PayoutConfigManager.instance) {
      PayoutConfigManager.instance = new PayoutConfigManager(envConfig);
    }
    return PayoutConfigManager.instance;
  }

  public static updateConfig(envConfig: PayoutEnvironmentConfig): void {
    if (PayoutConfigManager.instance) {
      PayoutConfigManager.instance.config = PayoutConfigManager.instance.buildConfigFromEnvironment(envConfig);
    }
  }

  private buildConfigFromEnvironment(envConfig?: PayoutEnvironmentConfig): PayoutConfig {
    const env = envConfig || process.env;

    // Determine primary gateway (Cashfree is primary for payouts)
    const primaryGateway: PayoutGateway = (env.PAYOUT_GATEWAY_PRIMARY as PayoutGateway) || "CASHFREE";
    
    // Determine fallback gateway
    const fallbackGateway: PayoutGateway | undefined = env.ENABLE_PAYOUT_GATEWAY_FALLBACK === "true" 
      ? (env.PAYOUT_GATEWAY_FALLBACK as PayoutGateway) || (primaryGateway === "CASHFREE" ? "RAZORPAY" : "CASHFREE")
      : undefined;

    const config: PayoutConfig = {
      primaryGateway,
      fallbackGateway,
    };

    // Add Razorpay configuration if needed
    if (primaryGateway === "RAZORPAY" || fallbackGateway === "RAZORPAY") {
      if (!env.RAZORPAY_KEY_ID || !env.RAZORPAY_SECRET_KEY) {
        throw new Error("Razorpay configuration is incomplete. RAZORPAY_KEY_ID and RAZORPAY_SECRET_KEY are required.");
      }
      
      config.razorpay = {
        keyId: env.RAZORPAY_KEY_ID,
        keySecret: env.RAZORPAY_SECRET_KEY,
        baseURL: env.RAZORPAY_BASE_URL,
      };
    }

    // Add Cashfree configuration if needed
    if (primaryGateway === "CASHFREE" || fallbackGateway === "CASHFREE") {
      if (!env.CASHFREE_APP_ID || !env.CASHFREE_SECRET_KEY) {
        throw new Error("Cashfree configuration is incomplete. CASHFREE_APP_ID and CASHFREE_SECRET_KEY are required.");
      }
      
      config.cashfree = {
        appId: env.CASHFREE_APP_ID,
        secretKey: env.CASHFREE_SECRET_KEY,
        environment: (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
      };
    }

    return config;
  }

  public getConfig(): PayoutConfig {
    return { ...this.config };
  }

  public getPrimaryGateway(): PayoutGateway {
    return this.config.primaryGateway;
  }

  public getFallbackGateway(): PayoutGateway | undefined {
    return this.config.fallbackGateway;
  }

  public hasFallback(): boolean {
    return !!this.config.fallbackGateway;
  }

  public isAnalyticsEnabled(): boolean {
    return process.env.ENABLE_PAYOUT_ANALYTICS === "true";
  }

  public getRazorpayConfig() {
    return this.config.razorpay;
  }

  public getCashfreeConfig() {
    return this.config.cashfree;
  }

  public validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate primary gateway configuration
    if (this.config.primaryGateway === "RAZORPAY" && !this.config.razorpay) {
      errors.push("Razorpay configuration is missing for primary gateway");
    }

    if (this.config.primaryGateway === "CASHFREE" && !this.config.cashfree) {
      errors.push("Cashfree configuration is missing for primary gateway");
    }

    // Validate fallback gateway configuration
    if (this.config.fallbackGateway === "RAZORPAY" && !this.config.razorpay) {
      errors.push("Razorpay configuration is missing for fallback gateway");
    }

    if (this.config.fallbackGateway === "CASHFREE" && !this.config.cashfree) {
      errors.push("Cashfree configuration is missing for fallback gateway");
    }

    // Validate that primary and fallback are different
    if (this.config.fallbackGateway && this.config.primaryGateway === this.config.fallbackGateway) {
      errors.push("Primary and fallback gateways cannot be the same");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  public getGatewayInfo() {
    return {
      primary: this.config.primaryGateway,
      fallback: this.config.fallbackGateway,
      hasFallback: this.hasFallback(),
      analyticsEnabled: this.isAnalyticsEnabled(),
    };
  }
}

// Utility functions for easy configuration
export function createPayoutConfig(envConfig?: PayoutEnvironmentConfig): PayoutConfig {
  const manager = PayoutConfigManager.getInstance(envConfig);
  return manager.getConfig();
}

export function validatePayoutConfig(envConfig?: PayoutEnvironmentConfig): { isValid: boolean; errors: string[] } {
  const manager = PayoutConfigManager.getInstance(envConfig);
  return manager.validateConfig();
}

export function getPayoutGatewayInfo(envConfig?: PayoutEnvironmentConfig) {
  const manager = PayoutConfigManager.getInstance(envConfig);
  return manager.getGatewayInfo();
}
