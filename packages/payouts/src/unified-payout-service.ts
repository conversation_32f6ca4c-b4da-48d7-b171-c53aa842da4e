import type {
  BulkPayoutRequest,
  BulkPayoutResponse,
  ContactResponse,
  CreateContactRequest,
  CreateFundAccountRequest,
  CreatePayoutRequest,
  FundAccountResponse,
  PayoutConfig,
  PayoutResponseContainer,
  PayoutResponse as PayoutResponseType,
  PayoutStatusResponse,
  ValidateFundAccountRequest,
  ValidateFundAccountResponse,
} from "./types";
import { PayoutFactory } from "./payout-factory";

export class UnifiedPayoutService {
  private factory: PayoutFactory;

  constructor(config: PayoutConfig) {
    this.factory = PayoutFactory.getInstance(config);
  }

  public static updateConfig(config: PayoutConfig): void {
    PayoutFactory.updateConfig(config);
  }

  // Contact operations
  async createContact(
    request: CreateContactRequest,
  ): Promise<PayoutResponseContainer<ContactResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createContact(request),
      "createContact",
    );
  }

  // Fund account operations
  async createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createFundAccount(request),
      "createFundAccount",
    );
  }

  async getAllFundAccounts(
    contactId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse[]>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.getAllFundAccounts(contactId),
      "getAllFundAccounts",
    );
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.deactivateFundAccount(fundAccountId),
      "deactivateFundAccount",
    );
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.reactivateFundAccount(fundAccountId),
      "reactivateFundAccount",
    );
  }

  // Payout operations
  async createPayout(
    request: CreatePayoutRequest,
  ): Promise<PayoutResponseContainer<PayoutResponseType>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.createPayout(request),
      "createPayout",
    );
  }

  async getPayoutStatus(
    payoutId: string,
  ): Promise<PayoutResponseContainer<PayoutStatusResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.getPayoutStatus(payoutId),
      "getPayoutStatus",
    );
  }

  // Bulk operations
  async createBulkPayout(
    request: BulkPayoutRequest,
  ): Promise<PayoutResponseContainer<BulkPayoutResponse>> {
    return this.factory.executeWithFallback(
      (gateway) =>
        gateway.createBulkPayout
          ? gateway.createBulkPayout(request)
          : Promise.reject(new Error("Bulk payout not supported")),
      "createBulkPayout",
    );
  }

  // Validation operations
  async validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PayoutResponseContainer<ValidateFundAccountResponse>> {
    return this.factory.executeWithFallback(
      (gateway) => gateway.validateFundAccount(request),
      "validateFundAccount",
    );
  }

  // Utility methods
  getCurrentGateway() {
    return this.factory.getCurrentGateway();
  }

  getFallbackGateway() {
    return this.factory.getFallbackGatewayType();
  }

  getGatewayInfo() {
    return this.factory.getGatewayInfo();
  }

  // Gateway-specific operations (when you need to use a specific gateway)
  async executeWithSpecificGateway<T>(
    gatewayType: "primary" | "fallback",
    operation: (gateway: any) => Promise<T>,
  ): Promise<T> {
    const gateway =
      gatewayType === "primary"
        ? this.factory.getPrimaryGateway()
        : this.factory.getFallbackGateway();

    if (!gateway) {
      throw new Error(`${gatewayType} gateway is not available`);
    }

    return operation(gateway);
  }
}
