// Common payout types and interfaces

export type PayoutGateway = "RAZORPAY" | "CASHFREE";

export interface PayoutConfig {
  primaryGateway: PayoutGateway;
  fallbackGateway?: PayoutGateway;
  razorpay?: {
    keyId: string;
    keySecret: string;
    baseURL?: string;
  };
  cashfree?: {
    appId: string;
    secretKey: string;
    environment: "sandbox" | "production";
  };
}

export interface PayoutResponseContainer<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

// Contact/Beneficiary interfaces
export interface CreateContactRequest {
  name: string;
  email?: string;
  contact?: string;
  type?: "customer" | "vendor" | "employee";
  reference_id?: string;
  notes?: Record<string, string>;
}

export interface ContactResponse {
  id: string;
  entity: string;
  name: string;
  contact?: string;
  email?: string;
  type: string;
  reference_id?: string;
  batch_id?: string;
  active: boolean;
  notes: Record<string, string>;
  created_at: number;
}

// Fund Account interfaces
export interface CreateFundAccountRequest {
  contact_id: string;
  account_type: "bank_account" | "vpa" | "card";
  bank_account?: {
    name: string;
    account_number: string;
    ifsc: string;
  };
  vpa?: {
    address: string;
  };
  card?: {
    name: string;
    number: string;
  };
}

export interface FundAccountResponse {
  id: string;
  entity: string;
  contact_id: string;
  account_type: string;
  bank_account?: {
    name: string;
    account_number: string;
    ifsc: string;
    bank_name: string;
  };
  vpa?: {
    address: string;
    handle: string;
  };
  card?: {
    name: string;
    last4: string;
    network: string;
    type: string;
    issuer: string;
  };
  batch_id?: string;
  active: boolean;
  created_at: number;
}

// Payout interfaces
export interface CreatePayoutRequest {
  fundsource_id: string;
  beneficiary_id: string;
  amount: number;
  currency: string;
  mode: "IMPS" | "NEFT" | "RTGS" | "UPI";
  purpose: string;
  queue_if_low_balance?: boolean;
  reference_id?: string;
  narration?: string;
  notes?: Record<string, string>;
}

export interface PayoutResponse {
  id: string;
  entity: string;
  fund_account_id: string;
  amount: number;
  currency: string;
  notes: Record<string, string>;
  fees: number;
  tax: number;
  status: string;
  purpose: string;
  utr?: string;
  mode: string;
  reference_id?: string;
  narration?: string;
  batch_id?: string;
  failure_reason?: string;
  created_at: number;
}

// Validation interfaces
export interface ValidateFundAccountRequest {
  fund_account: {
    id: string;
  };
  amount: number;
  currency: string;
  notes?: Record<string, string>;
}

export interface ValidateFundAccountResponse {
  id: string;
  entity: string;
  fund_account: FundAccountResponse;
  status: string;
  amount: number;
  currency: string;
  notes: Record<string, string>;
  results: {
    account_status: string;
    registered_name: string;
  };
  created_at: number;
}

// Bulk payout interfaces
export interface BulkPayoutRequest {
  payouts: CreatePayoutRequest[];
  reference_id?: string;
}

export interface BulkPayoutResponse {
  id: string;
  entity: string;
  status: string;
  reference_id?: string;
  total_payouts: number;
  successful_payouts: number;
  failed_payouts: number;
  pending_payouts: number;
  created_at: number;
}

// Payout status interfaces
export interface PayoutStatusRequest {
  payout_id: string;
}

export interface PayoutStatusResponse {
  id: string;
  status: string;
  amount: number;
  currency: string;
  utr?: string;
  failure_reason?: string;
  processed_at?: number;
}

// Abstract payout gateway interface
export interface IPayoutGateway {
  // Contact operations
  createContact(
    request: CreateContactRequest,
  ): Promise<PayoutResponseContainer<ContactResponse>>;

  // Fund account operations
  createFundAccount(
    request: CreateFundAccountRequest,
  ): Promise<PayoutResponseContainer<FundAccountResponse>>;
  getAllFundAccounts(
    contactId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse[]>>;
  deactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>>;
  reactivateFundAccount(
    fundAccountId: string,
  ): Promise<PayoutResponseContainer<FundAccountResponse>>;

  // Payout operations
  createPayout(
    request: CreatePayoutRequest,
  ): Promise<PayoutResponseContainer<PayoutResponse>>;
  getPayoutStatus(
    payoutId: string,
  ): Promise<PayoutResponseContainer<PayoutStatusResponse>>;

  // Bulk operations
  createBulkPayout?(
    request: BulkPayoutRequest,
  ): Promise<PayoutResponseContainer<BulkPayoutResponse>>;

  // Validation operations
  validateFundAccount(
    request: ValidateFundAccountRequest,
  ): Promise<PayoutResponseContainer<ValidateFundAccountResponse>>;
}
