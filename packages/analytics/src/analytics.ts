import { PostHog } from "posthog-js";

import type {
  AnalyticsConfig,
  AnalyticsEvent,
  EventName,
  EventProperties,
} from "./types";

// Platform detection TODO: check if this is correct
const isReactNative =
  typeof globalThis !== "undefined" &&
  typeof (globalThis as any).navigator !== "undefined" &&
  (globalThis as any).navigator.product === "ReactNative";

const isBrowser =
  typeof globalThis !== "undefined" &&
  typeof (globalThis as any).window !== "undefined";

export class Analytics {
  private config: AnalyticsConfig;
  private posthog: any = null;
  private initialized = false;
  private eventQueue: AnalyticsEvent[] = [];

  constructor(config: AnalyticsConfig) {
    this.config = config;
  }

  async initialize(): Promise<void> {
    if (this.initialized || this.config.disabled) {
      return;
    }

    try {
      // if (isReactNative) {
      //   // React Native initialization
      //   const PostHog = await import("posthog-react-native");
      //   this.posthog = PostHog.default;

      //   await this.posthog.setup(this.config.apiKey, {
      //     host: this.config.apiHost || "https://app.posthog.com",
      //     debug: this.config.debug || false,
      //   });
      // } else if (isBrowser) {
      // Web initialization
      this.posthog = PostHog;

      this.posthog.init(this.config.apiKey, {
        api_host: this.config.apiHost || "https://app.posthog.com",
        debug: this.config.debug || false,
        capture_pageview: false, // We'll handle this manually
        capture_pageleave: true,
        persistence: "localStorage",
      });
      // } else {
      //   // Server-side - no initialization needed
      //   console.warn("PostHog analytics not available on server-side");
      //   return;
      // }

      this.initialized = true;

      // Set user properties if provided
      if (this.config.userId) {
        this.identify(this.config.userId, this.config.userProperties);
      }

      // Process queued events
      this.processEventQueue();
    } catch (error) {
      console.error("Failed to initialize analytics:", error);
    }
  }

  private processEventQueue(): void {
    while (this.eventQueue.length > 0) {
      const event = this.eventQueue.shift();
      if (event) {
        this.trackEvent(event.name, event.properties);
      }
    }
  }

  private getEventName(eventName: EventName): string {
    return `${this.config.appPrefix}_${eventName}`;
  }

  identify(userId: string, properties?: Record<string, any>): void {
    if (!this.initialized || this.config.disabled) {
      return;
    }

    try {
      if (this.posthog) {
        this.posthog.identify(userId, {
          app_prefix: this.config.appPrefix,
          ...properties,
        });
      }
    } catch (error) {
      console.error("Failed to identify user:", error);
    }
  }

  trackEvent(eventName: EventName, properties: EventProperties = {}): void {
    if (this.config.disabled) {
      return;
    }

    const event: AnalyticsEvent = {
      name: eventName,
      properties: {
        app_prefix: this.config.appPrefix,
        timestamp: Date.now(),
        ...properties,
      },
    };

    if (!this.initialized) {
      // Queue event for later processing
      this.eventQueue.push(event);
      return;
    }

    try {
      if (this.posthog) {
        this.posthog.capture(this.getEventName(eventName), event.properties);
      }
    } catch (error) {
      console.error("Failed to track event:", error);
    }
  }

  // Payment Analytics
  trackPaymentInitiated(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    amount: number;
    currency: string;
    order_id: string;
    payment_method?: string;
  }): void {
    this.trackEvent("payment_initiated", properties);
  }

  trackPaymentSuccess(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    amount: number;
    currency: string;
    order_id: string;
    transaction_id: string;
    payment_method?: string;
    processing_time_ms?: number;
  }): void {
    this.trackEvent("payment_success", properties);
  }

  trackPaymentFailed(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    amount: number;
    currency: string;
    order_id: string;
    failure_reason: string;
    processing_time_ms?: number;
  }): void {
    this.trackEvent("payment_failed", properties);
  }

  // Payout Analytics
  trackPayoutInitiated(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    amount: number;
    currency: string;
    payout_id: string;
    beneficiary_id: string;
    mode: string;
    purpose: string;
  }): void {
    this.trackEvent("payout_initiated", properties);
  }

  trackPayoutSuccess(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    amount: number;
    currency: string;
    payout_id: string;
    beneficiary_id: string;
    processing_time_ms?: number;
  }): void {
    this.trackEvent("payout_success", properties);
  }

  trackPayoutFailed(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    amount: number;
    currency: string;
    payout_id: string;
    beneficiary_id: string;
    failure_reason: string;
  }): void {
    this.trackEvent("payout_failed", properties);
  }

  // Gateway Analytics
  trackGatewayRequest(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    operation: string;
    request_id?: string;
  }): void {
    this.trackEvent("gateway_request", properties);
  }

  trackGatewayResponse(properties: {
    gateway: "RAZORPAY" | "CASHFREE";
    operation: string;
    success: boolean;
    response_time_ms: number;
    error_code?: string;
    error_message?: string;
  }): void {
    this.trackEvent("gateway_response", properties);
  }

  trackGatewayFallback(properties: {
    primary_gateway: "RAZORPAY" | "CASHFREE";
    fallback_gateway: "RAZORPAY" | "CASHFREE";
    operation: string;
    primary_error: string;
    fallback_success: boolean;
  }): void {
    this.trackEvent("gateway_fallback", properties);
  }

  // User Analytics
  trackUserAction(action: string, properties: Record<string, any> = {}): void {
    this.trackEvent("feature_used", {
      action,
      ...properties,
    });
  }

  trackScreenView(
    screenName: string,
    properties: Record<string, any> = {},
  ): void {
    this.trackEvent("screen_viewed", {
      screen_name: screenName,
      ...properties,
    });
  }

  trackError(error: {
    type: string;
    message: string;
    stack?: string;
    severity: "low" | "medium" | "high" | "critical";
    context?: Record<string, any>;
  }): void {
    this.trackEvent("error_occurred", {
      error_type: error.type,
      error_message: error.message,
      error_stack: error.stack,
      severity: error.severity,
      ...error.context,
    });
  }

  // Performance Analytics
  trackPerformance(
    action: string,
    duration: number,
    success: boolean,
    metadata?: Record<string, any>,
  ): void {
    this.trackEvent("api_call", {
      action,
      duration_ms: duration,
      success,
      ...metadata,
    });
  }

  // Wallet Analytics
  trackWalletTransaction(properties: {
    user_id: string;
    user_type: "customer" | "kabadiwala" | "scraphub";
    transaction_type: "CREDIT" | "DEBIT";
    amount: number;
    currency: string;
    balance_before: number;
    balance_after: number;
    transaction_for: string;
  }): void {
    this.trackEvent("wallet_balance_updated", properties);
  }

  // Order Analytics
  trackOrderEvent(
    event: "created" | "updated" | "cancelled" | "completed",
    properties: {
      order_id: string;
      order_value: number;
      currency: string;
      items_count: number;
      customer_id?: string;
      kabadiwala_id?: string;
      scraphub_id?: string;
    },
  ): void {
    this.trackEvent(`order_${event}` as EventName, properties);
  }

  setUserProperties(properties: Record<string, any>): void {
    if (!this.initialized || this.config.disabled) {
      return;
    }

    try {
      if (this.posthog) {
        this.posthog.setPersonProperties(properties);
      }
    } catch (error) {
      console.error("Failed to set user properties:", error);
    }
  }

  reset(): void {
    if (!this.initialized || this.config.disabled) {
      return;
    }

    try {
      if (this.posthog) {
        this.posthog.reset();
      }
    } catch (error) {
      console.error("Failed to reset analytics:", error);
    }
  }

  shutdown(): void {
    if (!this.initialized || this.config.disabled) {
      return;
    }

    try {
      if (this.posthog && this.posthog.shutdown) {
        this.posthog.shutdown();
      }
    } catch (error) {
      console.error("Failed to shutdown analytics:", error);
    }
  }
}
