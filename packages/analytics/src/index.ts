import { Analytics } from "./analytics";

export { Analytics } from "./analytics";
export * from "./types";

// Singleton instances for each app
let customerAnalytics: Analytics | null = null;
let kabadiwalaAnalytics: Analytics | null = null;
let scraphubAnalytics: Analytics | null = null;

export function createAnalytics(config: {
  apiKey: string;
  apiHost?: string;
  appPrefix: "customer" | "kabadiwala" | "scraphub";
  userId?: string;
  userProperties?: Record<string, any>;
  debug?: boolean;
  disabled?: boolean;
}): Analytics {
  const analytics = new Analytics(config);

  // Store singleton reference
  switch (config.appPrefix) {
    case "customer":
      customerAnalytics = analytics;
      break;
    case "kabadiwala":
      kabadiwalaAnalytics = analytics;
      break;
    case "scraphub":
      scraphubAnalytics = analytics;
      break;
  }

  return analytics;
}

export function getAnalytics(
  appPrefix: "customer" | "kabadiwala" | "scraphub",
): Analytics | null {
  switch (appPrefix) {
    case "customer":
      return customerAnalytics;
    case "kabadiwala":
      return kabadiwalaAnalytics;
    case "scraphub":
      return scraphubAnalytics;
    default:
      return null;
  }
}
