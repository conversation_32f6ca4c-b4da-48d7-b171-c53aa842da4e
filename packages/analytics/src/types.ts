// Analytics types and interfaces

export type AppPrefix = "customer" | "kabadiwala" | "scraphub";

export interface AnalyticsConfig {
  apiKey: string;
  apiHost?: string;
  appPrefix: AppPrefix;
  userId?: string;
  userProperties?: Record<string, any>;
  debug?: boolean;
  disabled?: boolean;
}

// Payment Events
export interface PaymentEventProperties {
  gateway: "RAZORPAY" | "CASHFREE";
  amount: number;
  currency: string;
  order_id: string;
  transaction_id?: string;
  payment_method?: string;
  failure_reason?: string;
  processing_time_ms?: number;
}

export interface PayoutEventProperties {
  gateway: "RAZORPAY" | "CASHFREE";
  amount: number;
  currency: string;
  payout_id: string;
  beneficiary_id: string;
  mode: string;
  purpose: string;
  failure_reason?: string;
  processing_time_ms?: number;
}

// User Events
export interface UserEventProperties {
  user_id: string;
  user_type: "customer" | "kabadiwala" | "scraphub";
  action: string;
  timestamp?: number;
  session_id?: string;
}

// Order Events
export interface OrderEventProperties {
  order_id: string;
  order_value: number;
  currency: string;
  items_count: number;
  customer_id?: string;
  kabadiwala_id?: string;
  scraphub_id?: string;
  order_status: string;
  payment_method?: string;
}

// Wallet Events
export interface WalletEventProperties {
  user_id: string;
  user_type: "customer" | "kabadiwala" | "scraphub";
  transaction_type: "CREDIT" | "DEBIT";
  amount: number;
  currency: string;
  balance_before: number;
  balance_after: number;
  transaction_for: string;
}

// App-specific Events
export interface AppEventProperties {
  screen_name?: string;
  feature_name?: string;
  action: string;
  category?: string;
  value?: number;
  metadata?: Record<string, any>;
}

// Error Events
export interface ErrorEventProperties {
  error_type: string;
  error_message: string;
  error_stack?: string;
  user_id?: string;
  screen_name?: string;
  feature_name?: string;
  severity: "low" | "medium" | "high" | "critical";
}

// Performance Events
export interface PerformanceEventProperties {
  action: string;
  duration_ms: number;
  success: boolean;
  error_message?: string;
  metadata?: Record<string, any>;
}

// Gateway Analytics Events
export interface GatewayAnalyticsProperties {
  gateway: "RAZORPAY" | "CASHFREE";
  operation: string;
  success: boolean;
  response_time_ms: number;
  error_code?: string;
  error_message?: string;
  fallback_used?: boolean;
  retry_count?: number;
}

// Event names with app prefixes
export type EventName = 
  // Payment Events
  | "payment_initiated"
  | "payment_success"
  | "payment_failed"
  | "payment_gateway_switched"
  
  // Payout Events
  | "payout_initiated"
  | "payout_success"
  | "payout_failed"
  | "payout_gateway_switched"
  
  // User Events
  | "user_registered"
  | "user_login"
  | "user_logout"
  | "user_profile_updated"
  | "user_verification_started"
  | "user_verification_completed"
  | "user_approval_requested"
  | "user_approved"
  | "user_rejected"
  
  // Order Events
  | "order_created"
  | "order_updated"
  | "order_cancelled"
  | "order_completed"
  | "order_payment_initiated"
  | "order_payment_completed"
  
  // Wallet Events
  | "wallet_deposit_initiated"
  | "wallet_deposit_completed"
  | "wallet_withdrawal_initiated"
  | "wallet_withdrawal_completed"
  | "wallet_balance_updated"
  
  // App Events
  | "screen_viewed"
  | "feature_used"
  | "button_clicked"
  | "form_submitted"
  | "search_performed"
  | "filter_applied"
  
  // Error Events
  | "error_occurred"
  | "api_error"
  | "network_error"
  | "validation_error"
  
  // Performance Events
  | "page_load"
  | "api_call"
  | "database_query"
  | "file_upload"
  | "image_processing"
  
  // Gateway Events
  | "gateway_request"
  | "gateway_response"
  | "gateway_error"
  | "gateway_fallback";

export type EventProperties = 
  | PaymentEventProperties
  | PayoutEventProperties
  | UserEventProperties
  | OrderEventProperties
  | WalletEventProperties
  | AppEventProperties
  | ErrorEventProperties
  | PerformanceEventProperties
  | GatewayAnalyticsProperties
  | Record<string, any>;

export interface AnalyticsEvent {
  name: EventName;
  properties: EventProperties;
  timestamp?: number;
}
