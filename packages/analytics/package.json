{"name": "@acme/analytics", "version": "1.0.0", "description": "Unified analytics package with PostHog integration for all Scraplo apps", "main": "src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "prepare": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["analytics", "posthog", "tracking", "events", "typescript"], "author": "", "license": "ISC", "dependencies": {"posthog-js": "catalog:"}, "peerDependencies": {"typescript": "catalog:"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "catalog:"}}