import { TRPCError } from "@trpc/server";
import { z } from "zod";
import crypto from "crypto";

import { eq } from "@acme/db";
import { kabadiwalaPaymentTransaction } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../env";
import { createTRPCRouter, publicProcedure } from "../trpc";
import { unifiedPaymentService } from "./payment";

// Razorpay webhook schema
const RazorpayWebhookSchema = z.object({
  entity: z.string(),
  account_id: z.string(),
  event: z.string(),
  contains: z.array(z.string()),
  payload: z.object({
    payment: z.object({
      entity: z.object({
        id: z.string(),
        entity: z.string(),
        amount: z.number(),
        currency: z.string(),
        status: z.string(),
        order_id: z.string(),
        invoice_id: z.string().optional(),
        international: z.boolean(),
        method: z.string(),
        amount_refunded: z.number(),
        refund_status: z.string().optional(),
        captured: z.boolean(),
        description: z.string().optional(),
        card_id: z.string().optional(),
        bank: z.string().optional(),
        wallet: z.string().optional(),
        vpa: z.string().optional(),
        email: z.string(),
        contact: z.string(),
        notes: z.record(z.string()),
        fee: z.number().optional(),
        tax: z.number().optional(),
        error_code: z.string().optional(),
        error_description: z.string().optional(),
        error_source: z.string().optional(),
        error_step: z.string().optional(),
        error_reason: z.string().optional(),
        acquirer_data: z.object({
          rrn: z.string().optional(),
        }).optional(),
        created_at: z.number(),
      }),
    }),
  }),
  created_at: z.number(),
});

// Cashfree webhook schema
const CashfreeWebhookSchema = z.object({
  type: z.string(),
  order_id: z.string(),
  cf_order_id: z.string(),
  payment_details: z.object({
    cf_payment_id: z.string(),
    order_id: z.string(),
    entity: z.string(),
    payment_currency: z.string(),
    payment_amount: z.number(),
    payment_time: z.string(),
    payment_completion_time: z.string(),
    payment_status: z.string(),
    payment_message: z.string(),
    bank_reference: z.string(),
    auth_id: z.string(),
  }).optional(),
  signature: z.string(),
  timestamp: z.string(),
});

export const webhooksRouter = createTRPCRouter({
  // Razorpay webhook handler
  razorpay: publicProcedure
    .input(RazorpayWebhookSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Verify webhook signature (this should be done at the HTTP level)
        // For now, we'll process the webhook assuming it's verified
        
        const { payload } = input;
        const payment = payload.payment.entity;

        if (input.event === "payment.captured") {
          // Find the transaction by Razorpay order ID
          const { data: transaction, err } = await tryCatch(
            ctx.db.query.kabadiwalaPaymentTransaction.findFirst({
              where: eq(kabadiwalaPaymentTransaction.razorpayOrderId, payment.order_id),
            }),
          );

          if (err || !transaction) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Transaction not found",
            });
          }

          // Update transaction status
          await ctx.db
            .update(kabadiwalaPaymentTransaction)
            .set({
              status: "SUCCESS",
              razorpayPaymentId: payment.id,
              gatewayMetadata: {
                webhook_event: input.event,
                payment_method: payment.method,
                captured_at: payment.created_at,
                fee: payment.fee,
                tax: payment.tax,
              },
            })
            .where(eq(kabadiwalaPaymentTransaction.id, transaction.id));

          console.log(`Razorpay payment captured: ${payment.id} for order: ${payment.order_id}`);
        } else if (input.event === "payment.failed") {
          // Handle failed payment
          const { data: transaction, err } = await tryCatch(
            ctx.db.query.kabadiwalaPaymentTransaction.findFirst({
              where: eq(kabadiwalaPaymentTransaction.razorpayOrderId, payment.order_id),
            }),
          );

          if (transaction) {
            await ctx.db
              .update(kabadiwalaPaymentTransaction)
              .set({
                status: "FAILED",
                gatewayMetadata: {
                  webhook_event: input.event,
                  error_code: payment.error_code,
                  error_description: payment.error_description,
                  error_source: payment.error_source,
                  error_step: payment.error_step,
                  error_reason: payment.error_reason,
                },
              })
              .where(eq(kabadiwalaPaymentTransaction.id, transaction.id));

            console.log(`Razorpay payment failed: ${payment.id} for order: ${payment.order_id}`);
          }
        }

        return { success: true, message: "Webhook processed successfully" };
      } catch (error) {
        console.error("Razorpay webhook processing error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process webhook",
        });
      }
    }),

  // Cashfree webhook handler
  cashfree: publicProcedure
    .input(CashfreeWebhookSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Verify webhook signature
        const isValidSignature = unifiedPaymentService.executeWithSpecificGateway(
          "primary",
          async (gateway: any) => {
            if (gateway.verifyWebhookSignature) {
              return gateway.verifyWebhookSignature(
                JSON.stringify(input),
                input.timestamp,
                input.signature
              );
            }
            return true; // Skip verification if method not available
          }
        );

        if (!isValidSignature) {
          throw new TRPCError({
            code: "UNAUTHORIZED",
            message: "Invalid webhook signature",
          });
        }

        if (input.type === "PAYMENT_SUCCESS_WEBHOOK" && input.payment_details) {
          // Find the transaction by Cashfree order ID
          const { data: transaction, err } = await tryCatch(
            ctx.db.query.kabadiwalaPaymentTransaction.findFirst({
              where: eq(kabadiwalaPaymentTransaction.cashfreeOrderId, input.cf_order_id),
            }),
          );

          if (err || !transaction) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Transaction not found",
            });
          }

          // Update transaction status
          await ctx.db
            .update(kabadiwalaPaymentTransaction)
            .set({
              status: "SUCCESS",
              cashfreePaymentId: input.payment_details.cf_payment_id,
              gatewayMetadata: {
                webhook_type: input.type,
                payment_status: input.payment_details.payment_status,
                payment_message: input.payment_details.payment_message,
                bank_reference: input.payment_details.bank_reference,
                auth_id: input.payment_details.auth_id,
                payment_time: input.payment_details.payment_time,
                completion_time: input.payment_details.payment_completion_time,
              },
            })
            .where(eq(kabadiwalaPaymentTransaction.id, transaction.id));

          console.log(`Cashfree payment success: ${input.payment_details.cf_payment_id} for order: ${input.order_id}`);
        } else if (input.type === "PAYMENT_FAILED_WEBHOOK" && input.payment_details) {
          // Handle failed payment
          const { data: transaction, err } = await tryCatch(
            ctx.db.query.kabadiwalaPaymentTransaction.findFirst({
              where: eq(kabadiwalaPaymentTransaction.cashfreeOrderId, input.cf_order_id),
            }),
          );

          if (transaction) {
            await ctx.db
              .update(kabadiwalaPaymentTransaction)
              .set({
                status: "FAILED",
                gatewayMetadata: {
                  webhook_type: input.type,
                  payment_status: input.payment_details.payment_status,
                  payment_message: input.payment_details.payment_message,
                  failure_reason: input.payment_details.payment_message,
                },
              })
              .where(eq(kabadiwalaPaymentTransaction.id, transaction.id));

            console.log(`Cashfree payment failed: ${input.payment_details.cf_payment_id} for order: ${input.order_id}`);
          }
        }

        return { success: true, message: "Webhook processed successfully" };
      } catch (error) {
        console.error("Cashfree webhook processing error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process webhook",
        });
      }
    }),
});
