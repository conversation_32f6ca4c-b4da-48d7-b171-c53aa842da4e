import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { UnifiedPayoutService, createPayoutConfig } from "@acme/payouts";
import { eq } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaPaymentTransaction,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize unified payout service with Cashfree as primary
const payoutConfig = createPayoutConfig({
  PAYOUT_GATEWAY_PRIMARY: "CASHFREE",
  PAYOUT_GATEWAY_FALLBACK: "RAZORPAY",
  ENABLE_PAYOUT_GATEWAY_FALLBACK: "true",
  
  RAZORPAY_KEY_ID: env.RAZORPAY_KEY_ID,
  RAZORPAY_SECRET_KEY: env.RAZORPAY_SECRET_KEY,
  
  CASHFREE_APP_ID: env.CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY: env.CASHFREE_SECRET_KEY,
  CASHFREE_ENVIRONMENT: (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
});

const unifiedPayoutService = new UnifiedPayoutService(payoutConfig);

// Schemas
const CreateContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email().optional(),
  contact: z.string().optional(),
  type: z.enum(["customer", "vendor", "employee"]).default("customer"),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const CreateFundAccountSchema = z.object({
  contact_id: z.string(),
  account_type: z.enum(["bank_account", "vpa"]),
  bank_account: z.object({
    name: z.string(),
    account_number: z.string(),
    ifsc: z.string(),
  }).optional(),
  vpa: z.object({
    address: z.string(),
  }).optional(),
}).refine(
  (data) => {
    if (data.account_type === "bank_account" && !data.bank_account) {
      return false;
    }
    if (data.account_type === "vpa" && !data.vpa) {
      return false;
    }
    return true;
  },
  {
    message: "Account details must match the account type",
  }
);

const CreatePayoutSchema = z.object({
  fund_account_id: z.string(),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("INR"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string(),
  reference_id: z.string().optional(),
  narration: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

export const payoutRouter = createTRPCRouter({
  // Get payout gateway info
  getGatewayInfo: protectedProcedure.query(async () => {
    return unifiedPayoutService.getGatewayInfo();
  }),

  // Contact management
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.createContact(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Fund account management
  createFundAccount: protectedProcedure
    .input(CreateFundAccountSchema)
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.createFundAccount(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  getAllFundAccounts: protectedProcedure
    .input(z.object({ contact_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.getAllFundAccounts(input.contact_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data || [];
    }),

  deactivateFundAccount: protectedProcedure
    .input(z.object({ fund_account_id: z.string() }))
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.deactivateFundAccount(input.fund_account_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Payout operations
  createPayout: protectedProcedure
    .input(CreatePayoutSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if kabadiwala has sufficient balance
      const { data: kabadiwalaData, err: kabadiwalaErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            walletBalance: true,
          },
        }),
      );

      if (kabadiwalaErr || !kabadiwalaData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Kabadiwala not found",
        });
      }

      const currentBalance = Number(kabadiwalaData.walletBalance);
      if (currentBalance < input.amount) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient wallet balance",
        });
      }

      // Create payout
      const { data, error } = await unifiedPayoutService.createPayout(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      // Record payout transaction
      const { err: insertErr } = await tryCatch(
        ctx.db
          .insert(kabadiwalaPaymentTransaction)
          .values({
            kabadiwalaId: ctx.session.user.id,
            amount: input.amount.toString(),
            paymentGateway: unifiedPayoutService.getCurrentGateway(),
            status: "PENDING",
            currency: input.currency,
            transactionFor: "WITHDRAWAL",
            transactionType: "DEBIT",
            gatewayMetadata: {
              payout_id: data?.id,
              fund_account_id: input.fund_account_id,
              purpose: input.purpose,
              mode: input.mode,
            },
          }),
      );

      if (insertErr) {
        console.error("Failed to record payout transaction:", insertErr);
      }

      return data;
    }),

  getPayoutStatus: protectedProcedure
    .input(z.object({ payout_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.getPayoutStatus(input.payout_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Validation
  validateFundAccount: protectedProcedure
    .input(z.object({
      fund_account_id: z.string(),
      amount: z.number().positive(),
      currency: z.string().default("INR"),
    }))
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.validateFundAccount({
        fund_account: { id: input.fund_account_id },
        amount: input.amount,
        currency: input.currency,
      });
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Get kabadiwala wallet balance
  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaData, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          walletBalance: true,
        },
      }),
    );

    if (err || !kabadiwalaData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    return {
      balance: Number(kabadiwalaData.walletBalance),
      currency: "INR",
    };
  }),
});
