# Database
POSTGRES_URL="postgresql://username:password@localhost:5432/scraplo_db"

# Authentication
BETTER_AUTH_SECRET="your-auth-secret-here"
BETTER_AUTH_URL="http://localhost:3000"

# Payment Gateway Configuration
# Primary gateway (RAZORPAY or CASHFREE)
PAYMENT_GATEWAY_PRIMARY="RAZORPAY"

# Fallback gateway (optional, RAZORPAY or CASHFREE)
PAYMENT_GATEWAY_FALLBACK="CASHFREE"

# Enable fallback gateway (true/false)
ENABLE_PAYMENT_GATEWAY_FALLBACK="true"

# Razorpay Configuration
RAZORPAY_KEY_ID="rzp_test_your_key_id"
RAZORPAY_SECRET_KEY="your_razorpay_secret_key"

# Cashfree Configuration
CASHFREE_APP_ID="your_cashfree_app_id"
CASHFREE_SECRET_KEY="your_cashfree_secret_key"
CASHFREE_ENVIRONMENT="sandbox"  # or "production"

# Other services
UPLOADTHING_TOKEN="your_uploadthing_token"
DL_VERIFICATION_API_KEY="your_dl_verification_api_key"
GETSTREAM_API_KEY="your_getstream_api_key"
GETSTREAM_API_SECRET="your_getstream_api_secret"

# OneSignal
NEXT_PUBLIC_ONESIGNAL_KABADIWALA_APP_ID="your_onesignal_app_id"

# CometChat
NEXT_PUBLIC_COMET_CHAT_APP_ID="your_comet_chat_app_id"
NEXT_PUBLIC_COMET_CHAT_REGION="your_comet_chat_region"

# Analytics and Monitoring
ENABLE_PAYMENT_GATEWAY_ANALYTICS="true"

# Mobile App Specific (for .env.development in mobile app)
API_URL="http://localhost:3000/api"
GET_STREAM_API_KEY="your_getstream_api_key"
