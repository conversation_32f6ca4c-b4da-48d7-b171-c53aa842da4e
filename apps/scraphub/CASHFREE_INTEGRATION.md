# Cashfree Integration Guide for ScrapHub

## Overview

ScrapHub uses two different Cashfree packages for payment processing:

1. **Frontend (Next.js)**: `@cashfreepayments/pg-react` - For client-side payment handling
2. **Backend (tRPC APIs)**: `cashfree-pg` - For server-side payment operations

## Frontend Integration

### Installation
```bash
pnpm add @cashfreepayments/pg-react --filter=scraphub
```

### Usage in React Components

```typescript
import { load } from "@cashfreepayments/pg-react";

const handleCashfreePayment = async (orderData: any) => {
  try {
    // Load Cashfree SDK
    const cashfree = await load({
      mode: process.env.NODE_ENV === "production" ? "production" : "sandbox",
    });

    // Configure checkout options
    const checkoutOptions = {
      paymentSessionId: orderData.payment_session_id,
      redirectTarget: "_modal", // Opens in modal
    };

    // Start payment process
    const result = await cashfree.checkout(checkoutOptions);
    
    if (result.error) {
      console.error("Payment failed:", result.error);
    } else if (result.redirect) {
      // Payment completed successfully
      console.log("Payment successful:", result.paymentDetails);
    }
  } catch (error) {
    console.error("Cashfree error:", error);
  }
};
```

### Environment Variables

Add these to your `.env.local`:

```env
NEXT_PUBLIC_CASHFREE_APP_ID=your_app_id
NEXT_PUBLIC_CASHFREE_ENVIRONMENT=sandbox # or production
```

## Backend Integration

### Installation
```bash
pnpm add cashfree-pg --filter=@acme/cashfree-sdk
```

### Usage in Server Code

```typescript
import { Cashfree } from "cashfree-pg";

// Initialize Cashfree
Cashfree.XClientId = process.env.CASHFREE_APP_ID;
Cashfree.XClientSecret = process.env.CASHFREE_SECRET_KEY;
Cashfree.XEnvironment = process.env.CASHFREE_ENVIRONMENT === "production" 
  ? Cashfree.Environment.PRODUCTION 
  : Cashfree.Environment.SANDBOX;

// Create order
const orderRequest = {
  order_id: "order_123",
  order_amount: 1000,
  order_currency: "INR",
  customer_details: {
    customer_id: "customer_123",
    customer_name: "John Doe",
    customer_email: "<EMAIL>",
    customer_phone: "9999999999",
  },
};

const response = await Cashfree.PGCreateOrder("2023-08-01", orderRequest);
```

## Current Implementation

### Deposit Modal (`apps/scraphub/src/components/deposit/deposit-modal.tsx`)

The deposit modal now supports both Razorpay and Cashfree payments:

- **Razorpay**: Uses `react-razorpay` package
- **Cashfree**: Uses `@cashfreepayments/pg-react` package

### Payment Router (`apps/scraphub/src/server/api/router/payment.ts`)

The payment router creates orders using the unified payment service which supports both gateways.

### Cashfree SDK (`packages/cashfree/src/cashfree.ts`)

Updated to use the official `cashfree-pg` package for:
- Order creation
- Payment verification
- Beneficiary management
- Transfer/payout operations

## Key Differences

### Frontend Packages
- **React Native/Expo**: Use Cashfree's native SDKs
- **Next.js/React**: Use `@cashfreepayments/pg-react`

### Backend Packages
- **All server environments**: Use `cashfree-pg`

## Testing

### Sandbox Credentials
- App ID: Test app ID from Cashfree dashboard
- Secret Key: Test secret key from Cashfree dashboard
- Environment: `sandbox`

### Test Cards
Use Cashfree's test card numbers for sandbox testing.

## Production Deployment

1. Update environment variables with production credentials
2. Set `CASHFREE_ENVIRONMENT=production`
3. Ensure webhook URLs are configured in Cashfree dashboard
4. Test with small amounts before going live

## Security Notes

- Never expose secret keys in frontend code
- Use environment variables for all sensitive data
- Validate all payments on the server side
- Implement proper webhook verification
