"use client";

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { emailSchema } from "@acme/validators";

import { authClient } from "~/server/auth/client";

const ResetPasswordSchema = z.object({
  email: emailSchema,
});

export default function ForgetPasswordPage() {
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof ResetPasswordSchema>) => {
    setIsLoading(true);
    try {
      await authClient.forgetPassword(
        {
          email: values.email,
          redirectTo: `${window.location.origin}/auth/reset-password`,
        },
        {
          onSuccess: (val) => {
            console.log("success val ", val);
            setSubmitted(true);
            toast.success("Reset password link sent to your email");
          },
          onError: (error) => {
            console.error("Error sending reset link:", error);
            toast.error("Failed to send reset link. Please try again.");
          },
        },
      );
    } catch (error) {
      console.error(error);
      toast.error("Failed to send reset link. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        <Link
          href="/"
          className="flex w-full items-center justify-center gap-2 self-center text-lg font-medium"
        >
          <Image
            src="/static/logo/scraplo.svg"
            alt="scraplo"
            height={500}
            width={500}
            className="size-10"
          />
          Scraplo Scraphub
        </Link>
        
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl">Reset Your Password</CardTitle>
          </CardHeader>
          <CardContent>
            {submitted ? (
              <div className="text-center space-y-4">
                <p className="text-muted-foreground">
                  We've sent a password reset link to your email address.
                </p>
                <p className="text-sm text-muted-foreground">
                  Please check your inbox and follow the instructions to reset
                  your password.
                </p>
                <Link
                  href="/auth/login"
                  className="text-sm text-teal-600 hover:text-teal-500 font-medium"
                >
                  Back to Login
                </Link>
              </div>
            ) : (
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-6"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? "Sending..." : "Send Reset Link"}
                  </Button>
                  
                  <div className="text-center text-sm text-gray-600">
                    Remember your password?{" "}
                    <Link
                      href="/auth/login"
                      className="font-medium text-teal-600 hover:text-teal-500"
                    >
                      Login here
                    </Link>
                  </div>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
