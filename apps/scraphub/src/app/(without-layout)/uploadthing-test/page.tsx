"use client";

import { UploadThingDemo } from "~/components/shared";

export default function UploadThingTestPage() {
  const handleImagesUploaded = (urls: string[]) => {
    console.log("Images uploaded:", urls);
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="mb-8 text-3xl font-bold">UploadThing Integration Test</h1>

      <div className="max-w-2xl space-y-8">
        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Image Upload Demo</h2>
          <UploadThingDemo
            onImagesUploaded={handleImagesUploaded}
            maxImages={4}
          />
        </div>

        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Usage Instructions</h2>
          <div className="space-y-2 text-sm text-gray-600">
            <p>• Click the upload button to select images</p>
            <p>• Supported formats: JPEG, PNG, WebP</p>
            <p>• Max file size: 4MB</p>
            <p>• Max images: 4</p>
            <p>• Authentication required</p>
          </div>
        </div>

        <div className="rounded-lg border p-6">
          <h2 className="mb-4 text-xl font-semibold">Integration Status</h2>
          <div className="space-y-2 text-sm">
            <p className="text-green-600">
              ✅ UploadThing API routes configured
            </p>
            <p className="text-green-600">✅ File router with authentication</p>
            <p className="text-green-600">✅ Client utilities created</p>
            <p className="text-green-600">✅ Demo component available</p>
            <p className="text-green-600">
              ✅ tRPC utilities for file deletion
            </p>
            <p className="text-green-600">
              ✅ Next.js image domains configured
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
