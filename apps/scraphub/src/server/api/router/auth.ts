import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { eq } from "drizzle-orm";
import { z } from "zod";

import {
  scraphub,
  scraphubAddress,
  scraphubEmployee,
  scraphubEmployeeAccount,
} from "@acme/db/schema";
import { phoneNumberSchema } from "@acme/validators";

import { addressSchema } from "~/components/onboarding/types";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";

// Initial signup - just create user account
const initialSignupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

// Step 1: Organization Details
const organizationDetailsSchema = z.object({
  organizationName: z.string().min(1, "Organization name is required"),
  organizationType: z.enum([
    "PRIVATE_LIMITED",
    "PUBLIC_LIMITED",
    "PARTNERSHIP",
    "PROPRIETORSHIP",
    "LLP",
    "TRUST",
    "SOCIETY",
    "OTHER",
  ]),
  phoneNumber: phoneNumberSchema,
  registeredOfficeAddress: z
    .string()
    .min(1, "Registered office address is required"),
  communicationAddress: z.string().min(1, "Communication address is required"),
});

// Step 2: Legal Documents
const legalDocumentsSchema = z.object({
  cinNumber: z.string().min(1, "CIN number is required"),
  cinCertificateKey: z.string().min(1, "CIN certificate is required"),
  gstNumber: z.string().min(1, "GST number is required"),
  gstCertificateKey: z.string().min(1, "GST certificate is required"),
  panNumber: z.string().min(1, "PAN number is required"),
  panCertificateKey: z.string().min(1, "PAN certificate is required"),
});

// Step 3: Director Information
const directorInfoSchema = z.object({
  directorName: z.string().min(1, "Director name is required"),
  directorAadharKey: z.string().min(1, "Director Aadhar is required"),
});

export const authRouter = createTRPCRouter({
  // Initial signup - create user account only
  initialSignup: publicProcedure
    .input(initialSignupSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Check if user with same email already exists
        const existingUser = await ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.email, input.email),
        });

        if (existingUser) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "A user with this email already exists",
          });
        }

        // Create a placeholder organization (will be updated in onboarding)
        const [newOrg] = await ctx.db
          .insert(scraphub)
          .values({
            name: "", // Placeholder name
            onboardingStep: "EMAIL_VERIFIED",
            signupCompleted: false,
          })
          .returning({ id: scraphub.id });

        if (!newOrg) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create organization placeholder",
          });
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(input.password, 10);

        // Create the primary user account
        const [newUser] = await ctx.db
          .insert(scraphubEmployee)
          .values({
            scraphubId: newOrg.id,
            name: input.name,
            email: input.email,
            emailVerified: false,
          })
          .returning({ id: scraphubEmployee.id });

        if (!newUser) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create user account",
          });
        }

        // Create the Better Auth account record
        await ctx.db.insert(scraphubEmployeeAccount).values({
          accountId: newUser.id,
          providerId: "email",
          scraphubEmployeeId: newUser.id,
          password: hashedPassword,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        return {
          success: true,
          message:
            "Account created successfully. Please check your email to verify your account.",
          organizationId: newOrg.id,
          userId: newUser.id,
        };
      } catch (error) {
        console.error("Initial signup error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create account",
        });
      }
    }),

  // Step 1: Save organization details
  saveOrganizationDetails: protectedProcedure
    .input(organizationDetailsSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.session.user.id;

      // Get user's organization
      const user = await ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, userId),
        with: {
          scraphub: true,
        },
      });

      // // Check if organization with same name already exists (excluding current org)
      // const existingOrg = await ctx.db.query.scraphub.findFirst({
      //   where: eq(scraphub.name, input.organizationName),
      // });

      // if (existingOrg && existingOrg.id !== user.scraphub.id) {
      //   throw new TRPCError({
      //     code: "CONFLICT",
      //     message: "An organization with this name already exists",
      //   });
      // }

      // Perform all updates in a single transaction
      await ctx.db.transaction(async (tx) => {
        if (!user?.scraphub) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Update organization details
        await tx
          .update(scraphub)
          .set({
            name: input.organizationName,
            organizationType: input.organizationType,
            phoneNumber: input.phoneNumber,
            registeredOfficeAddress: input.registeredOfficeAddress,
            communicationAddress: input.communicationAddress,
            onboardingStep: "ORGANIZATION_DETAILS",
            updatedAt: new Date(),
          })
          .where(eq(scraphub.id, user.scraphub.id));
      });

      return {
        success: true,
        message: "Organization details saved successfully",
      };
    }),

  // Step 2: Save legal documents
  saveLegalDocuments: protectedProcedure
    .input(legalDocumentsSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const userId = ctx.session.user.id;

        // Get user's organization
        const user = await ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, userId),
          with: {
            scraphub: true,
          },
        });

        if (!user?.scraphub) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Organization not found",
          });
        }

        // TODO: Handle file uploads to cloud storage
        // For now, we'll store file references as strings

        // Update organization with legal documents
        await ctx.db
          .update(scraphub)
          .set({
            cinNumber: input.cinNumber,
            cinCertificateFileKey: input.cinCertificateKey || null,
            gstNumber: input.gstNumber,
            gstCertificateFileKey: input.gstCertificateKey || null,
            panNumber: input.panNumber,
            panCertificateFileKey: input.panCertificateKey || null,
            onboardingStep: "LEGAL_DOCUMENTS",
            updatedAt: new Date(),
          })
          .where(eq(scraphub.id, user.scraphub.id));

        return {
          success: true,
          message: "Legal documents saved successfully",
        };
      } catch (error) {
        console.error("Save legal documents error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to save legal documents",
        });
      }
    }),

  // Step 3: Save director information
  saveDirectorInfo: protectedProcedure
    .input(directorInfoSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const userId = ctx.session.user.id;

        // Get user's organization
        const user = await ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, userId),
          with: {
            scraphub: {
              with: {
                address: true,
              },
            },
          },
        });

        if (!user?.scraphub) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Organization not found",
          });
        }

        // TODO: Handle file uploads to cloud storage

        // Update organization with director information
        await ctx.db
          .update(scraphub)
          .set({
            directorName: input.directorName,
            directorAadharFileKey: input.directorAadharKey || null,
            onboardingStep: "ADDRESS",
            updatedAt: new Date(),
          })
          .where(eq(scraphub.id, user.scraphub.id));

        return {
          success: true,
          message:
            "Director information saved successfully. Onboarding completed!",
        };
      } catch (error) {
        console.error("Save director info error:", error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to save director information",
        });
      }
    }),

  // Step 4: Save address
  saveAddress: protectedProcedure
    .input(addressSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.session.user.id;

      // Get user's organization
      const user = await ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, userId || ""),
        with: {
          scraphub: true,
        },
      });

      if (!user?.scraphub) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Organization not found",
        });
      }

      const address = await ctx.db.query.scraphubAddress.findFirst({
        where: eq(scraphubAddress.scraphubId, user.scraphub.id),
      });

      if (address) {
        await ctx.db
          .update(scraphubAddress)
          .set({
            display: input.display,
          })
          .where(eq(scraphubAddress.id, address.id));
      } else {
        await ctx.db.insert(scraphubAddress).values({
          display: input.display,
          street: input.street,
          city: input.city,
          state: input.state,
          country: input.country,
          postalCode: input.postalCode,
          scraphubId: user.scraphub.id,
          localAddress: input.localAddress,
          landmark: input.landmark,
          coordinates: input.coordinates,
        });
      }

      // Update organization with address
      await ctx.db
        .update(scraphub)
        .set({
          onboardingStep: "COMPLETED",
          updatedAt: new Date(),
        })
        .where(eq(scraphub.id, user.scraphub.id));

      return {
        success: true,
        message: "Address saved successfully. Onboarding completed!",
      };
    }),

  // Get current onboarding status
  getOnboardingStatus: protectedProcedure.query(async ({ ctx }) => {
    try {
      const userId = ctx.session.user.id;

      const user = await ctx.db.query.scraphubEmployee.findFirst({
        where: eq(scraphubEmployee.id, userId),
        with: {
          scraphub: {
            with: {
              address: true,
            },
          },
        },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      if (!user?.scraphub) {
        // If the user does not have a scraphub, create one and associate it with the user
        const newScraphub = await ctx.db
          .insert(scraphub)
          .values({
            name: user.name + " Organization",
            onboardingStep: "EMAIL_VERIFIED",
            signupCompleted: false,
          })
          .returning();

        // Update the user to associate with the new scraphub
        await ctx.db
          .update(scraphubEmployee)
          .set({ scraphubId: newScraphub[0]?.id || "" })
          .where(eq(scraphubEmployee.id, userId));

        // Refetch the user with the new scraphub
        const userWithScraphub = await ctx.db.query.scraphubEmployee.findFirst({
          where: eq(scraphubEmployee.id, userId),
          with: {
            scraphub: {
              with: {
                address: true,
              },
            },
          },
        });

        if (!userWithScraphub?.scraphub) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to associate user with organization",
          });
        }

        return {
          onboardingStep: userWithScraphub.scraphub.onboardingStep,
          signupCompleted: userWithScraphub.scraphub.signupCompleted,
          organizationName: userWithScraphub.scraphub.name,
          organizationDetails: {
            organizationName: userWithScraphub.scraphub.name,
            organizationType: userWithScraphub.scraphub.organizationType,
            phoneNumber: userWithScraphub.scraphub.phoneNumber,
            registeredOfficeAddress:
              userWithScraphub.scraphub.registeredOfficeAddress,
            communicationAddress:
              userWithScraphub.scraphub.communicationAddress,
          },
          legalDocuments: {
            cinNumber: userWithScraphub.scraphub.cinNumber,
            cinCertificateKey: userWithScraphub.scraphub.cinCertificateFileKey,
            gstNumber: userWithScraphub.scraphub.gstNumber,
            gstCertificateKey: userWithScraphub.scraphub.gstCertificateFileKey,
            panNumber: userWithScraphub.scraphub.panNumber,
            panCertificateKey: userWithScraphub.scraphub.panCertificateFileKey,
          },
          directorInfo: {
            directorName: userWithScraphub.scraphub.directorName,
            directorAadharKey: userWithScraphub.scraphub.directorAadharFileKey,
          },
          address: {
            display: userWithScraphub.scraphub?.address?.display || "",
            street: userWithScraphub.scraphub?.address?.street || "",
            city: userWithScraphub.scraphub?.address?.city || "",
            state: userWithScraphub.scraphub?.address?.state || "",
            country: userWithScraphub.scraphub?.address?.country || "",
            postalCode: userWithScraphub.scraphub?.address?.postalCode || "",
            coordinates:
              userWithScraphub.scraphub?.address?.coordinates || null,
            localAddress:
              userWithScraphub.scraphub?.address?.localAddress || "",
            landmark: userWithScraphub.scraphub?.address?.landmark || "",
          },
        };
      }

      return {
        onboardingStep: user.scraphub.onboardingStep,
        signupCompleted: user.scraphub.signupCompleted,
        organizationName: user.scraphub.name,
        organizationDetails: {
          organizationName: user.scraphub.name,
          organizationType: user.scraphub.organizationType,
          phoneNumber: user.scraphub.phoneNumber,
          registeredOfficeAddress: user.scraphub.registeredOfficeAddress,
          communicationAddress: user.scraphub.communicationAddress,
        },
        legalDocuments: {
          cinNumber: user.scraphub.cinNumber,
          cinCertificateKey: user.scraphub.cinCertificateFileKey,
          gstNumber: user.scraphub.gstNumber,
          gstCertificateKey: user.scraphub.gstCertificateFileKey,
          panNumber: user.scraphub.panNumber,
          panCertificateKey: user.scraphub.panCertificateFileKey,
        },
        directorInfo: {
          directorName: user.scraphub.directorName,
          directorAadharKey: user.scraphub.directorAadharFileKey,
        },
        address: {
          display: user.scraphub?.address?.display || "",
          street: user.scraphub?.address?.street || "",
          city: user.scraphub?.address?.city || "",
          state: user.scraphub?.address?.state || "",
          country: user.scraphub?.address?.country || "",
          postalCode: user.scraphub.address?.postalCode || "",
          coordinates: user.scraphub?.address?.coordinates || null,
          localAddress: user.scraphub?.address?.localAddress || "",
          landmark: user.scraphub?.address?.landmark || "",
        },
      };
    } catch (error) {
      console.error("Get onboarding status error:", error);

      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get onboarding status",
      });
    }
  }),

  // Add other auth procedures as needed
  verifyEmail: publicProcedure
    .input(z.object({ token: z.string() }))
    .mutation(async ({ input }) => {
      // This would be handled by Better Auth's verification flow
      throw new TRPCError({
        code: "NOT_IMPLEMENTED",
        message: "Email verification is handled by Better Auth",
      });
    }),
});
