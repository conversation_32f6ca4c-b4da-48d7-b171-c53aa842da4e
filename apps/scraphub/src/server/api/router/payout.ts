import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { UnifiedPayoutService, createPayoutConfig } from "@acme/payouts";
import { eq } from "@acme/db";
import {
  scraphub,
  scraphubPaymentTransaction,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import analytics from "../../../lib/analytics";

// Initialize unified payout service with Cashfree as primary
const payoutConfig = createPayoutConfig({
  PAYOUT_GATEWAY_PRIMARY: "CASHFREE",
  PAYOUT_GATEWAY_FALLBACK: "RAZORPAY",
  ENABLE_PAYOUT_GATEWAY_FALLBACK: "true",
  
  RAZORPAY_KEY_ID: env.RAZORPAY_KEY_ID,
  RAZORPAY_SECRET_KEY: env.RAZORPAY_SECRET_KEY,
  
  CASHFREE_APP_ID: env.CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY: env.CASHFREE_SECRET_KEY,
  CASHFREE_ENVIRONMENT: (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
});

const unifiedPayoutService = new UnifiedPayoutService(payoutConfig);

// Schemas
const CreateContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email().optional(),
  contact: z.string().optional(),
  type: z.enum(["customer", "vendor", "employee"]).default("vendor"),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const CreateFundAccountSchema = z.object({
  contact_id: z.string(),
  account_type: z.enum(["bank_account", "vpa"]),
  bank_account: z.object({
    name: z.string(),
    account_number: z.string(),
    ifsc: z.string(),
  }).optional(),
  vpa: z.object({
    address: z.string(),
  }).optional(),
}).refine(
  (data) => {
    if (data.account_type === "bank_account" && !data.bank_account) {
      return false;
    }
    if (data.account_type === "vpa" && !data.vpa) {
      return false;
    }
    return true;
  },
  {
    message: "Account details must match the account type",
  }
);

const CreatePayoutSchema = z.object({
  fund_account_id: z.string(),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("INR"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string(),
  reference_id: z.string().optional(),
  narration: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const BulkPayoutSchema = z.object({
  payouts: z.array(CreatePayoutSchema),
  reference_id: z.string().optional(),
});

export const payoutRouter = createTRPCRouter({
  // Get payout gateway info
  getGatewayInfo: protectedProcedure.query(async () => {
    return unifiedPayoutService.getGatewayInfo();
  }),

  // Contact management
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.createContact(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      // Track contact creation
      analytics.trackEvent("feature_used", {
        action: "contact_created",
        contact_type: input.type,
        gateway: unifiedPayoutService.getCurrentGateway(),
      });

      return data;
    }),

  // Fund account management
  createFundAccount: protectedProcedure
    .input(CreateFundAccountSchema)
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.createFundAccount(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      // Track fund account creation
      analytics.trackEvent("feature_used", {
        action: "fund_account_created",
        account_type: input.account_type,
        gateway: unifiedPayoutService.getCurrentGateway(),
      });

      return data;
    }),

  getAllFundAccounts: protectedProcedure
    .input(z.object({ contact_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.getAllFundAccounts(input.contact_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data || [];
    }),

  deactivateFundAccount: protectedProcedure
    .input(z.object({ fund_account_id: z.string() }))
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.deactivateFundAccount(input.fund_account_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Payout operations
  createPayout: protectedProcedure
    .input(CreatePayoutSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if ScrapHub has sufficient balance and is approved
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, ctx.session.user.scraphubId!),
          columns: {
            walletBalance: true,
            adminApprovalStatus: true,
          },
        }),
      );

      if (scraphubErr || !scraphubData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Your account must be approved before making payouts",
        });
      }

      const currentBalance = Number(scraphubData.walletBalance);
      if (currentBalance < input.amount) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient wallet balance",
        });
      }

      // Track payout initiation
      analytics.trackPayoutInitiated({
        gateway: unifiedPayoutService.getCurrentGateway(),
        amount: input.amount,
        currency: input.currency,
        payout_id: `pending_${Date.now()}`,
        beneficiary_id: input.fund_account_id,
        mode: input.mode,
        purpose: input.purpose,
      });

      // Create payout
      const { data, error } = await unifiedPayoutService.createPayout(input);
      
      if (error) {
        analytics.trackPayoutFailed({
          gateway: unifiedPayoutService.getCurrentGateway(),
          amount: input.amount,
          currency: input.currency,
          payout_id: `failed_${Date.now()}`,
          beneficiary_id: input.fund_account_id,
          failure_reason: error.message,
        });

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      // Record payout transaction
      const { err: insertErr } = await tryCatch(
        ctx.db
          .insert(scraphubPaymentTransaction)
          .values({
            scraphubId: ctx.session.user.scraphubId!,
            amount: input.amount.toString(),
            paymentGateway: unifiedPayoutService.getCurrentGateway(),
            status: "PENDING",
            currency: input.currency,
            transactionFor: "WITHDRAWAL",
            transactionType: "DEBIT",
            description: `Payout of ₹${input.amount.toLocaleString()} - ${input.purpose}`,
            gatewayMetadata: {
              payout_id: data?.id,
              fund_account_id: input.fund_account_id,
              purpose: input.purpose,
              mode: input.mode,
            },
          }),
      );

      if (insertErr) {
        console.error("Failed to record payout transaction:", insertErr);
      }

      // Track successful payout creation
      analytics.trackPayoutSuccess({
        gateway: unifiedPayoutService.getCurrentGateway(),
        amount: input.amount,
        currency: input.currency,
        payout_id: data?.id || "unknown",
        beneficiary_id: input.fund_account_id,
      });

      return data;
    }),

  // Bulk payout operations
  createBulkPayout: protectedProcedure
    .input(BulkPayoutSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if ScrapHub has sufficient balance and is approved
      const { data: scraphubData, err: scraphubErr } = await tryCatch(
        ctx.db.query.scraphub.findFirst({
          where: eq(scraphub.id, ctx.session.user.scraphubId!),
          columns: {
            walletBalance: true,
            adminApprovalStatus: true,
          },
        }),
      );

      if (scraphubErr || !scraphubData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "ScrapHub not found",
        });
      }

      if (scraphubData.adminApprovalStatus !== "APPROVED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Your account must be approved before making payouts",
        });
      }

      const totalAmount = input.payouts.reduce((sum, payout) => sum + payout.amount, 0);
      const currentBalance = Number(scraphubData.walletBalance);
      
      if (currentBalance < totalAmount) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient wallet balance for bulk payout",
        });
      }

      // Create bulk payout
      const { data, error } = await unifiedPayoutService.createBulkPayout(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      // Track bulk payout
      analytics.trackEvent("feature_used", {
        action: "bulk_payout_created",
        total_amount: totalAmount,
        payout_count: input.payouts.length,
        gateway: unifiedPayoutService.getCurrentGateway(),
      });

      return data;
    }),

  getPayoutStatus: protectedProcedure
    .input(z.object({ payout_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.getPayoutStatus(input.payout_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Validation
  validateFundAccount: protectedProcedure
    .input(z.object({
      fund_account_id: z.string(),
      amount: z.number().positive(),
      currency: z.string().default("INR"),
    }))
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.validateFundAccount({
        fund_account: { id: input.fund_account_id },
        amount: input.amount,
        currency: input.currency,
      });
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Get ScrapHub wallet balance
  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data: scraphubData, err } = await tryCatch(
      ctx.db.query.scraphub.findFirst({
        where: eq(scraphub.id, ctx.session.user.scraphubId!),
        columns: {
          walletBalance: true,
          adminApprovalStatus: true,
        },
      }),
    );

    if (err || !scraphubData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "ScrapHub not found",
      });
    }

    return {
      balance: Number(scraphubData.walletBalance),
      currency: "INR",
      canPayout: scraphubData.adminApprovalStatus === "APPROVED",
    };
  }),
});
