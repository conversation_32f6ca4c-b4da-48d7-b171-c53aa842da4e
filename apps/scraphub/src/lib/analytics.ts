import { createAnalytics } from "@acme/analytics";

// Initialize ScrapHub analytics
export const analytics = createAnalytics({
  apiKey: process.env.NEXT_PUBLIC_POSTHOG_KEY || "",
  apiHost: process.env.NEXT_PUBLIC_POSTHOG_HOST,
  appPrefix: "scraphub",
  debug: process.env.NODE_ENV === "development",
  disabled: !process.env.NEXT_PUBLIC_POSTHOG_KEY,
});

// Initialize analytics on client side
if (typeof window !== "undefined") {
  analytics.initialize();
}

export default analytics;
