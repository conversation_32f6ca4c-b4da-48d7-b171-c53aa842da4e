"use client";

import { useQuery } from "@tanstack/react-query";
import { AlertCircle, Clock, XCircle } from "lucide-react";

import { Alert, AlertDescription } from "@acme/ui/components/ui/alert";
import { Button } from "@acme/ui/components/ui/button";

import { useTRPC } from "~/trpc/react";

export function ApprovalStatusBanner() {
  const trpc = useTRPC();
  const { data: walletInfo, isLoading } = useQuery(
    trpc.payment.getWalletInfo.queryOptions(),
  );

  if (isLoading || !walletInfo) {
    return null;
  }

  // Only show banner if not approved
  if (walletInfo.adminApprovalStatus === "APPROVED") {
    return null;
  }

  const getStatusConfig = () => {
    switch (walletInfo.adminApprovalStatus) {
      case "PENDING":
        return {
          icon: Clock,
          variant: "default" as const,
          className: "border-amber-200 bg-amber-50",
          iconClassName: "text-amber-600",
          textClassName: "text-amber-800",
          title: "Account Approval Pending",
          description:
            "Your ScrapHub account is currently under review by our admin team. You'll be able to make deposits once approved.",
          showContactButton: false,
        };
      case "REJECTED":
        return {
          icon: XCircle,
          variant: "destructive" as const,
          className: "border-red-200 bg-red-50",
          iconClassName: "text-red-600",
          textClassName: "text-red-800",
          title: "Account Approval Rejected",
          description:
            walletInfo.adminRejectionReason ||
            "Your account has been rejected. Please contact our support team for assistance.",
          showContactButton: true,
        };
      default:
        return {
          icon: AlertCircle,
          variant: "default" as const,
          className: "border-gray-200 bg-gray-50",
          iconClassName: "text-gray-600",
          textClassName: "text-gray-800",
          title: "Account Status Unknown",
          description:
            "There's an issue with your account status. Please contact support.",
          showContactButton: true,
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Alert className={`mb-6 ${config.className}`}>
      <Icon className={`h-4 w-4 ${config.iconClassName}`} />
      <div className="flex w-full items-center justify-between">
        <div>
          <div className={`font-medium ${config.textClassName}`}>
            {config.title}
          </div>
          <AlertDescription className={config.textClassName}>
            {config.description}
          </AlertDescription>
        </div>
        {config.showContactButton && (
          <Button variant="outline" size="sm" className="ml-4">
            Contact Support
          </Button>
        )}
      </div>
    </Alert>
  );
}
