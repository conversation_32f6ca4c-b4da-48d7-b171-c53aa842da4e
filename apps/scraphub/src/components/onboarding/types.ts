import { z } from "zod";

import { phoneNumberSchema } from "@acme/validators";

// Organization Details Schema
export const organizationDetailsSchema = z.object({
  organizationName: z.string().min(1, "Organization name is required"),
  organizationType: z.string().min(1, "Organization type is required"),
  phoneNumber: phoneNumberSchema,
  registeredOfficeAddress: z
    .string()
    .min(1, "Registered office address is required"),
  communicationAddress: z.string().min(1, "Communication address is required"),
});

// Legal Documents Schema
export const legalDocumentsSchema = z.object({
  cinNumber: z.string().min(1, "CIN number is required"),
  cinCertificateKey: z.string().min(1, "CIN certificate is required"),
  gstNumber: z
    .string()
    .min(1, "GST number is required")
    .regex(
      /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/i,
      "Enter a valid GST number",
    ),
  gstCertificateKey: z.string().min(1, "GST certificate is required"),
  panNumber: z
    .string()
    .min(1, "PAN number is required")
    .regex(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/i, "Enter a valid PAN number"),
  panCertificateKey: z.string().min(1, "PAN certificate is required"),
});

// Address Schema
export const addressSchema = z.object({
  display: z.string().min(1, "Address is required"),
  street: z.string().min(1, "Street is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  country: z.string().min(1, "Country is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  localAddress: z.string().min(1, "Local address is required"),
  landmark: z.string().min(1, "Landmark is required"),
});

// Director Information Schema
export const directorInfoSchema = z.object({
  directorName: z.string().min(1, "Director name is required"),
  directorAadharKey: z.string().min(1, "Director Aadhar is required"),
});

// Type exports
export type OrganizationDetailsForm = z.infer<typeof organizationDetailsSchema>;
export type LegalDocumentsForm = z.infer<typeof legalDocumentsSchema>;
export type AddressForm = z.infer<typeof addressSchema>;
export type DirectorInfoForm = z.infer<typeof directorInfoSchema>;

// Upload state tracking
export type UploadState = Record<string, boolean>;
