import { useEffect } from "react";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Textarea } from "@acme/ui/components/ui/textarea";

import type { OrganizationDetailsForm } from "./types";
import { organizationDetailsSchema } from "./types";

interface OrganizationDetailsFormProps {
  onSubmit: (data: OrganizationDetailsForm) => Promise<void>;
  onBack?: () => void;
  isSubmitting: boolean;
  initialData?: Partial<OrganizationDetailsForm>;
}

export function OrganizationDetailsForm({
  onSubmit,
  onBack,
  isSubmitting,
  initialData,
}: OrganizationDetailsFormProps) {
  const form = useForm<OrganizationDetailsForm>({
    resolver: zodResolver(organizationDetailsSchema),
    defaultValues: {
      organizationName: "",
      organizationType: "",
      phoneNumber: "",
      registeredOfficeAddress: "",
      communicationAddress: "",
    },
  });

  // Prefill form with initial data when available
  useEffect(() => {
    console.log("initialData", initialData);
    if (initialData) {
      form.reset({
        organizationName: initialData.organizationName || "",
        organizationType: initialData.organizationType || "",
        phoneNumber: initialData.phoneNumber || "",
        registeredOfficeAddress: initialData.registeredOfficeAddress || "",
        communicationAddress: initialData.communicationAddress || "",
      });
    }
  }, [initialData, form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="organizationName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Organization Name *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="Enter organization name"
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="organizationType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type of Organization *</FormLabel>
              <Select onValueChange={field.onChange} value={field.value || ""}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select organization type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="PRIVATE_LIMITED">
                    Private Limited
                  </SelectItem>
                  <SelectItem value="PUBLIC_LIMITED">Public Limited</SelectItem>
                  <SelectItem value="PARTNERSHIP">Partnership</SelectItem>
                  <SelectItem value="PROPRIETORSHIP">Proprietorship</SelectItem>
                  <SelectItem value="LLP">LLP</SelectItem>
                  <SelectItem value="TRUST">Trust</SelectItem>
                  <SelectItem value="SOCIETY">Society</SelectItem>
                  <SelectItem value="OTHER">Other</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number *</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  type="tel"
                  placeholder="Enter phone number (10 digits)"
                  value={field.value || ""}
                  onChange={field.onChange}
                  pattern="[0-9]{10}"
                  maxLength={10}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="registeredOfficeAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Registered Office Address *</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Enter registered office address"
                  rows={3}
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="communicationAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Address for Communication *</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Enter communication address"
                  rows={3}
                  value={field.value || ""}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-center gap-4 pt-4">
          {onBack && (
            <Button
              type="button"
              variant="outline"
              onClick={onBack}
              disabled={isSubmitting}
            >
              Back
            </Button>
          )}
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-teal-600 px-8 hover:bg-teal-700"
          >
            {isSubmitting ? "Saving..." : "Save & Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
