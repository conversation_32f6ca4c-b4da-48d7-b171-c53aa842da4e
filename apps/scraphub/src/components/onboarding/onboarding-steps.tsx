import { Building2, FileText, MapPin, User } from "lucide-react";

const steps = [
  { id: 1, title: "Organization Details", icon: Building2 },
  { id: 2, title: "Legal Documents", icon: FileText },
  { id: 3, title: "Director Information", icon: User },
  { id: 4, title: "Address", icon: MapPin },
];

interface OnboardingStepsProps {
  currentStep: number;
}

export function OnboardingSteps({ currentStep }: OnboardingStepsProps) {
  return (
    <div className="flex items-center justify-between">
      {steps.map((step) => {
        const Icon = step.icon;
        const isActive = currentStep === step.id;
        const isCompleted = currentStep > step.id;

        return (
          <div key={step.id} className="flex flex-col items-center space-y-2">
            <div
              className={`flex h-10 w-10 items-center justify-center rounded-full border-2 ${
                isActive
                  ? "border-teal-600 bg-teal-550 text-white"
                  : isCompleted
                    ? "border-green-600 bg-green-600 text-white"
                    : "border-gray-300 bg-gray-100 text-gray-400"
              }`}
            >
              {isCompleted ? (
                <span className="text-sm">✓</span>
              ) : (
                <Icon className="h-5 w-5" />
              )}
            </div>
            <span
              className={`text-center text-xs ${
                isActive ? "font-medium text-teal-600" : "text-gray-500"
              }`}
            >
              {step.title}
            </span>
          </div>
        );
      })}
    </div>
  );
}
