"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { CheckCircle, Clock, Loader2, XCircle } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Button } from "@acme/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Textarea } from "@acme/ui/components/ui/textarea";

import { useTRPC } from "~/trpc/react";

const ApprovalSchema = z.object({
  adminNotes: z.string().optional(),
});

const RejectionSchema = z.object({
  rejectionReason: z.string().min(1, "Rejection reason is required"),
});

type ApprovalFormData = z.infer<typeof ApprovalSchema>;
type RejectionFormData = z.infer<typeof RejectionSchema>;

interface ApprovalModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  scraphubId: string;
  scraphubName: string;
  currentStatus: "PENDING" | "APPROVED" | "REJECTED";
  action: "approve" | "reject" | "reset";
}

export function ApprovalModal({
  open,
  onOpenChange,
  scraphubId,
  scraphubName,
  currentStatus,
  action,
}: ApprovalModalProps) {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const approvalForm = useForm<ApprovalFormData>({
    resolver: zodResolver(ApprovalSchema),
    defaultValues: {
      adminNotes: "",
    },
  });

  const rejectionForm = useForm<RejectionFormData>({
    resolver: zodResolver(RejectionSchema),
    defaultValues: {
      rejectionReason: "",
    },
  });

  const { mutate: approveScraphub, isPending: isApproving } = useMutation(
    trpc.scraphub.approveScraphub.mutationOptions({
      onSuccess: (data) => {
        toast.success(data.message);
        void queryClient.invalidateQueries(
          trpc.scraphub.getAllScraphubs.queryOptions(),
        );
        onOpenChange(false);
        approvalForm.reset();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const { mutate: rejectScraphub, isPending: isRejecting } = useMutation(
    trpc.scraphub.rejectScraphub.mutationOptions({
      onSuccess: (data) => {
        toast.success(data.message);
        void queryClient.invalidateQueries(
          trpc.scraphub.getAllScraphubs.queryOptions(),
        );
        onOpenChange(false);
        rejectionForm.reset();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const { mutate: resetApprovalStatus, isPending: isResetting } = useMutation(
    trpc.scraphub.resetApprovalStatus.mutationOptions({
      onSuccess: (data) => {
        toast.success(data.message);
        void queryClient.invalidateQueries(
          trpc.scraphub.getAllScraphubs.queryOptions(),
        );
        onOpenChange(false);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const handleApprove = (data: ApprovalFormData) => {
    approveScraphub({
      scraphubId,
      adminNotes: data.adminNotes,
    });
  };

  const handleReject = (data: RejectionFormData) => {
    rejectScraphub({
      scraphubId,
      rejectionReason: data.rejectionReason,
    });
  };

  const handleReset = () => {
    resetApprovalStatus({ scraphubId });
  };

  const getModalConfig = () => {
    switch (action) {
      case "approve":
        return {
          title: "Approve ScrapHub Account",
          description: `Are you sure you want to approve ${scraphubName}? This will allow them to make deposits and use all ScrapHub features.`,
          icon: <CheckCircle className="h-6 w-6 text-green-600" />,
          buttonText: "Approve Account",
          buttonClass: "bg-green-600 hover:bg-green-700",
        };
      case "reject":
        return {
          title: "Reject ScrapHub Account",
          description: `Are you sure you want to reject ${scraphubName}? Please provide a reason for rejection.`,
          icon: <XCircle className="h-6 w-6 text-red-600" />,
          buttonText: "Reject Account",
          buttonClass: "bg-red-600 hover:bg-red-700",
        };
      case "reset":
        return {
          title: "Reset Approval Status",
          description: `Are you sure you want to reset the approval status for ${scraphubName} back to pending?`,
          icon: <Clock className="h-6 w-6 text-blue-600" />,
          buttonText: "Reset to Pending",
          buttonClass: "bg-blue-600 hover:bg-blue-700",
        };
    }
  };

  const config = getModalConfig();
  const isLoading = isApproving || isRejecting || isResetting;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {config.icon}
            {config.title}
          </DialogTitle>
          <DialogDescription>{config.description}</DialogDescription>
        </DialogHeader>

        {action === "approve" && (
          <Form {...approvalForm}>
            <form
              onSubmit={approvalForm.handleSubmit(handleApprove)}
              className="space-y-4"
            >
              <FormField
                control={approvalForm.control}
                name="adminNotes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Admin Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any notes for the ScrapHub..."
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      These notes will be included in the approval notification
                      email.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className={`flex-1 text-white ${config.buttonClass}`}
                >
                  {isApproving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Approving...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      {config.buttonText}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}

        {action === "reject" && (
          <Form {...rejectionForm}>
            <form
              onSubmit={rejectionForm.handleSubmit(handleReject)}
              className="space-y-4"
            >
              <FormField
                control={rejectionForm.control}
                name="rejectionReason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rejection Reason *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Please provide a clear reason for rejection..."
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      This reason will be sent to the ScrapHub in the rejection
                      notification.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex gap-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={isLoading}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className={`flex-1 text-white ${config.buttonClass}`}
                >
                  {isRejecting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Rejecting...
                    </>
                  ) : (
                    <>
                      <XCircle className="mr-2 h-4 w-4" />
                      {config.buttonText}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}

        {action === "reset" && (
          <div className="space-y-4">
            <div className="rounded-lg bg-blue-50 p-4">
              <p className="text-sm text-blue-800">
                This will reset the approval status to "PENDING" and clear any
                previous approval or rejection data.
              </p>
            </div>

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleReset}
                disabled={isLoading}
                className={`flex-1 text-white ${config.buttonClass}`}
              >
                {isResetting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Resetting...
                  </>
                ) : (
                  <>
                    <Clock className="mr-2 h-4 w-4" />
                    {config.buttonText}
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
