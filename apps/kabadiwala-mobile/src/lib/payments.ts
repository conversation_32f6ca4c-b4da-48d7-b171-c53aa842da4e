import { UnifiedPaymentService, createPaymentConfig } from "@acme/payments";
import { Env } from "./env";

// Create payment configuration based on environment variables
const paymentConfig = createPaymentConfig({
  PAYMENT_GATEWAY_PRIMARY: (Env.PAYMENT_GATEWAY_PRIMARY as "RAZORPAY" | "CASHFREE") || "RAZORPAY",
  PAYMENT_GATEWAY_FALLBACK: Env.PAYMENT_GATEWAY_FALLBACK as "RAZORPAY" | "CASHFREE",
  ENABLE_PAYMENT_GATEWAY_FALLBACK: Env.ENABLE_PAYMENT_GATEWAY_FALLBACK,
  
  // Razorpay configuration
  RAZORPAY_KEY_ID: Env.RAZORPAY_KEY_ID,
  RAZORPAY_SECRET_KEY: Env.RAZORPAY_SECRET_KEY,
  
  // Cashfree configuration
  CASHFREE_APP_ID: Env.CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY: Env.CASHFREE_SECRET_KEY,
  CASHFREE_ENVIRONMENT: (Env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
});

// Create unified payment service instance
export const paymentService = new UnifiedPaymentService(paymentConfig);

// Export payment gateway info for debugging
export const getPaymentGatewayInfo = () => paymentService.getGatewayInfo();

// Helper function to get current gateway type
export const getCurrentPaymentGateway = () => paymentService.getCurrentGateway();

// Helper function to check if fallback is available
export const hasFallbackGateway = () => !!paymentService.getFallbackGateway();
