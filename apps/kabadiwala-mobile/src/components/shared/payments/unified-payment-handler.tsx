import React from "react";
import { Alert } from "react-native";
import RazorpayCheckout from "react-native-razorpay";
import { CFPaymentGatewayService, CFSession } from "react-native-cashfree-pg-sdk";
import { Env } from "@/lib/env";
import { getCurrentPaymentGateway } from "@/lib/payments";

export interface PaymentOptions {
  orderId: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  customerEmail?: string;
  customerPhone?: string;
  customerName?: string;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  orderId: string;
  signature?: string;
  error?: string;
}

export class UnifiedPaymentHandler {
  private static async handleRazorpayPayment(options: PaymentOptions): Promise<PaymentResult> {
    try {
      const razorpayOptions = {
        order_id: options.orderId,
        key: Env.RAZORPAY_KEY_ID,
        name: options.name,
        amount: options.amount * 100, // Convert to paise
        currency: options.currency,
        description: options.description,
        prefill: {
          email: options.customerEmail,
          contact: options.customerPhone,
          name: options.customerName,
        },
      };

      const result = await RazorpayCheckout.open(razorpayOptions);
      
      return {
        success: true,
        paymentId: result.razorpay_payment_id,
        orderId: options.orderId,
        signature: result.razorpay_signature,
      };
    } catch (error: any) {
      console.error("Razorpay payment failed:", error);
      return {
        success: false,
        orderId: options.orderId,
        error: error.description || error.message || "Payment failed",
      };
    }
  }

  private static async handleCashfreePayment(options: PaymentOptions): Promise<PaymentResult> {
    try {
      // Initialize Cashfree SDK
      CFPaymentGatewayService.setCallback({
        onVerify: (orderID: string) => {
          console.log("Cashfree payment verification for order:", orderID);
        },
        onError: (error: any, orderID: string) => {
          console.error("Cashfree payment error for order:", orderID, error);
        },
      });

      // Create Cashfree session
      const session = new CFSession(
        options.orderId,
        options.amount,
        options.currency,
        Env.CASHFREE_ENVIRONMENT || "sandbox"
      );

      // Set customer details
      if (options.customerEmail || options.customerPhone || options.customerName) {
        session.setCustomerDetails(
          options.customerName || "",
          options.customerEmail || "",
          options.customerPhone || ""
        );
      }

      // Start payment
      const result = await CFPaymentGatewayService.doPayment(session);
      
      if (result && result.orderStatus === "PAID") {
        return {
          success: true,
          paymentId: result.txnId,
          orderId: options.orderId,
        };
      } else {
        return {
          success: false,
          orderId: options.orderId,
          error: result?.txnMsg || "Payment failed",
        };
      }
    } catch (error: any) {
      console.error("Cashfree payment failed:", error);
      return {
        success: false,
        orderId: options.orderId,
        error: error.message || "Payment failed",
      };
    }
  }

  public static async processPayment(options: PaymentOptions): Promise<PaymentResult> {
    const currentGateway = getCurrentPaymentGateway();
    
    console.log(`Processing payment with ${currentGateway} gateway`);
    
    try {
      let result: PaymentResult;
      
      if (currentGateway === "RAZORPAY") {
        result = await this.handleRazorpayPayment(options);
      } else if (currentGateway === "CASHFREE") {
        result = await this.handleCashfreePayment(options);
      } else {
        throw new Error(`Unsupported payment gateway: ${currentGateway}`);
      }

      if (!result.success) {
        // Show error alert
        Alert.alert(
          "Payment Failed",
          result.error || "An error occurred during payment processing",
          [{ text: "OK" }]
        );
      }

      return result;
    } catch (error: any) {
      console.error("Payment processing error:", error);
      
      const errorResult: PaymentResult = {
        success: false,
        orderId: options.orderId,
        error: error.message || "Payment processing failed",
      };

      Alert.alert(
        "Payment Error",
        errorResult.error,
        [{ text: "OK" }]
      );

      return errorResult;
    }
  }

  public static showPaymentInfo() {
    const currentGateway = getCurrentPaymentGateway();
    Alert.alert(
      "Payment Gateway",
      `Currently using: ${currentGateway}`,
      [{ text: "OK" }]
    );
  }
}

// Hook for using unified payment handler in components
export const useUnifiedPayment = () => {
  const processPayment = async (options: PaymentOptions): Promise<PaymentResult> => {
    return UnifiedPaymentHandler.processPayment(options);
  };

  const showGatewayInfo = () => {
    UnifiedPaymentHandler.showPaymentInfo();
  };

  const getCurrentGateway = () => {
    return getCurrentPaymentGateway();
  };

  return {
    processPayment,
    showGatewayInfo,
    getCurrentGateway,
  };
};
