import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

import { api } from "~/utils/api";

interface BankAccount {
  name: string;
  account_number: string;
  ifsc: string;
}

interface FundAccount {
  id: string;
  contact_id: string;
  account_type: "bank_account" | "vpa";
  bank_account?: BankAccount;
  vpa?: { address: string };
  active: boolean;
}

export default function PayoutScreen() {
  const [amount, setAmount] = useState("");
  const [selectedAccount, setSelectedAccount] = useState<FundAccount | null>(null);
  const [purpose, setPurpose] = useState("");
  const [mode, setMode] = useState<"IMPS" | "NEFT" | "RTGS" | "UPI">("IMPS");
  const [isProcessing, setIsProcessing] = useState(false);

  // Get wallet balance
  const { data: walletData, isLoading: isLoadingWallet } = api.payout.getWalletBalance.useQuery();

  // Get gateway info
  const { data: gatewayInfo } = api.payout.getGatewayInfo.useQuery();

  // Create payout mutation
  const createPayoutMutation = api.payout.createPayout.useMutation({
    onSuccess: (data) => {
      Alert.alert(
        "Payout Initiated",
        `Your payout of ₹${amount} has been initiated successfully. Payout ID: ${data?.id}`,
        [
          {
            text: "OK",
            onPress: () => {
              setAmount("");
              setPurpose("");
              router.back();
            },
          },
        ]
      );
    },
    onError: (error) => {
      Alert.alert("Payout Failed", error.message);
    },
    onSettled: () => {
      setIsProcessing(false);
    },
  });

  const handleCreatePayout = () => {
    if (!selectedAccount) {
      Alert.alert("Error", "Please select a bank account");
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert("Error", "Please enter a valid amount");
      return;
    }

    if (!purpose.trim()) {
      Alert.alert("Error", "Please enter the purpose of payout");
      return;
    }

    const payoutAmount = parseFloat(amount);
    const availableBalance = walletData?.balance || 0;

    if (payoutAmount > availableBalance) {
      Alert.alert("Error", "Insufficient wallet balance");
      return;
    }

    Alert.alert(
      "Confirm Payout",
      `Are you sure you want to transfer ₹${amount} to your bank account?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Confirm",
          onPress: () => {
            setIsProcessing(true);
            createPayoutMutation.mutate({
              fund_account_id: selectedAccount.id,
              amount: payoutAmount,
              currency: "INR",
              mode,
              purpose,
              narration: `Kabadiwala payout - ${purpose}`,
            });
          },
        },
      ]
    );
  };

  if (isLoadingWallet) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="mt-4 text-gray-600">Loading wallet information...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center justify-between border-b border-gray-200 px-4 py-3">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-900">Withdraw Money</Text>
        <View style={{ width: 24 }} />
      </View>

      <ScrollView className="flex-1 px-4 py-6">
        {/* Wallet Balance */}
        <View className="mb-6 rounded-lg bg-blue-50 p-4">
          <Text className="text-sm text-blue-600">Available Balance</Text>
          <Text className="text-2xl font-bold text-blue-900">
            ₹{walletData?.balance?.toLocaleString() || "0"}
          </Text>
          {gatewayInfo && (
            <Text className="mt-1 text-xs text-blue-600">
              Gateway: {gatewayInfo.primary}
              {gatewayInfo.hasFallback && ` (Fallback: ${gatewayInfo.fallback})`}
            </Text>
          )}
        </View>

        {/* Amount Input */}
        <View className="mb-6">
          <Text className="mb-2 text-sm font-medium text-gray-700">Amount (₹)</Text>
          <TextInput
            className="rounded-lg border border-gray-300 px-4 py-3 text-base"
            placeholder="Enter amount"
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
            editable={!isProcessing}
          />
        </View>

        {/* Purpose Input */}
        <View className="mb-6">
          <Text className="mb-2 text-sm font-medium text-gray-700">Purpose</Text>
          <TextInput
            className="rounded-lg border border-gray-300 px-4 py-3 text-base"
            placeholder="Enter purpose (e.g., Salary, Commission)"
            value={purpose}
            onChangeText={setPurpose}
            editable={!isProcessing}
          />
        </View>

        {/* Transfer Mode */}
        <View className="mb-6">
          <Text className="mb-2 text-sm font-medium text-gray-700">Transfer Mode</Text>
          <View className="flex-row flex-wrap gap-2">
            {(["IMPS", "NEFT", "RTGS", "UPI"] as const).map((modeOption) => (
              <TouchableOpacity
                key={modeOption}
                className={`rounded-lg border px-4 py-2 ${
                  mode === modeOption
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300 bg-white"
                }`}
                onPress={() => setMode(modeOption)}
                disabled={isProcessing}
              >
                <Text
                  className={`text-sm font-medium ${
                    mode === modeOption ? "text-blue-700" : "text-gray-700"
                  }`}
                >
                  {modeOption}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Bank Account Selection */}
        <View className="mb-6">
          <Text className="mb-2 text-sm font-medium text-gray-700">Bank Account</Text>
          <TouchableOpacity
            className="rounded-lg border border-gray-300 p-4"
            onPress={() => {
              // Navigate to bank account selection screen
              router.push("/payout/select-account");
            }}
            disabled={isProcessing}
          >
            {selectedAccount ? (
              <View>
                <Text className="font-medium text-gray-900">
                  {selectedAccount.bank_account?.name || "UPI Account"}
                </Text>
                <Text className="text-sm text-gray-600">
                  {selectedAccount.account_type === "bank_account"
                    ? `${selectedAccount.bank_account?.account_number} - ${selectedAccount.bank_account?.ifsc}`
                    : selectedAccount.vpa?.address}
                </Text>
              </View>
            ) : (
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-500">Select bank account</Text>
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </View>
            )}
          </TouchableOpacity>
        </View>

        {/* Payout Button */}
        <TouchableOpacity
          className={`rounded-lg py-4 ${
            isProcessing || !selectedAccount || !amount || !purpose
              ? "bg-gray-300"
              : "bg-blue-600"
          }`}
          onPress={handleCreatePayout}
          disabled={isProcessing || !selectedAccount || !amount || !purpose}
        >
          {isProcessing ? (
            <View className="flex-row items-center justify-center">
              <ActivityIndicator size="small" color="white" />
              <Text className="ml-2 text-center font-semibold text-white">
                Processing...
              </Text>
            </View>
          ) : (
            <Text className="text-center font-semibold text-white">
              Withdraw ₹{amount || "0"}
            </Text>
          )}
        </TouchableOpacity>

        {/* Info */}
        <View className="mt-6 rounded-lg bg-gray-50 p-4">
          <Text className="text-xs text-gray-600">
            • Withdrawals are processed using {gatewayInfo?.primary || "Cashfree"} gateway
            {gatewayInfo?.hasFallback && " with automatic fallback support"}
          </Text>
          <Text className="mt-1 text-xs text-gray-600">
            • IMPS transfers are usually instant, NEFT may take 2-4 hours
          </Text>
          <Text className="mt-1 text-xs text-gray-600">
            • You will receive a confirmation once the transfer is completed
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
