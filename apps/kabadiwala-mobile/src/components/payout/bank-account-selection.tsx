import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

import { api } from "~/utils/api";

interface BankAccount {
  name: string;
  account_number: string;
  ifsc: string;
  bank_name?: string;
}

interface FundAccount {
  id: string;
  contact_id: string;
  account_type: "bank_account" | "vpa";
  bank_account?: BankAccount;
  vpa?: { address: string; handle?: string };
  active: boolean;
  created_at: number;
}

export default function BankAccountSelection() {
  const [selectedAccountId, setSelectedAccountId] = useState<string | null>(null);

  // For demo purposes, we'll use a mock contact ID
  // In a real app, this would come from the user's profile or be created dynamically
  const mockContactId = "contact_kabadiwala_123";

  // Get fund accounts
  const { data: fundAccounts, isLoading, refetch } = api.payout.getAllFundAccounts.useQuery(
    { contact_id: mockContactId },
    {
      enabled: !!mockContactId,
    }
  );

  const handleSelectAccount = (account: FundAccount) => {
    setSelectedAccountId(account.id);
    
    Alert.alert(
      "Account Selected",
      `Selected: ${account.bank_account?.name || account.vpa?.address}`,
      [
        {
          text: "OK",
          onPress: () => {
            // In a real app, you would pass this selection back to the parent component
            // For now, we'll just navigate back
            router.back();
          },
        },
      ]
    );
  };

  const handleAddAccount = () => {
    Alert.alert(
      "Add Bank Account",
      "This feature would allow you to add a new bank account or UPI ID for withdrawals.",
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: "Add Account",
          onPress: () => {
            // Navigate to add account screen
            router.push("/payout/add-account");
          },
        },
      ]
    );
  };

  const formatAccountNumber = (accountNumber: string) => {
    if (accountNumber.length <= 4) return accountNumber;
    const masked = "*".repeat(accountNumber.length - 4);
    return masked + accountNumber.slice(-4);
  };

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="mt-4 text-gray-600">Loading bank accounts...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center justify-between border-b border-gray-200 px-4 py-3">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-900">Select Account</Text>
        <TouchableOpacity onPress={handleAddAccount}>
          <Ionicons name="add" size={24} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      <ScrollView className="flex-1 px-4 py-6">
        {fundAccounts && fundAccounts.length > 0 ? (
          <View className="space-y-4">
            {fundAccounts.map((account) => (
              <TouchableOpacity
                key={account.id}
                className={`rounded-lg border p-4 ${
                  selectedAccountId === account.id
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 bg-white"
                }`}
                onPress={() => handleSelectAccount(account)}
              >
                <View className="flex-row items-center justify-between">
                  <View className="flex-1">
                    {account.account_type === "bank_account" && account.bank_account ? (
                      <View>
                        <Text className="font-semibold text-gray-900">
                          {account.bank_account.name}
                        </Text>
                        <Text className="text-sm text-gray-600">
                          {formatAccountNumber(account.bank_account.account_number)}
                        </Text>
                        <Text className="text-sm text-gray-600">
                          IFSC: {account.bank_account.ifsc}
                        </Text>
                        {account.bank_account.bank_name && (
                          <Text className="text-sm text-gray-500">
                            {account.bank_account.bank_name}
                          </Text>
                        )}
                      </View>
                    ) : account.vpa ? (
                      <View>
                        <Text className="font-semibold text-gray-900">UPI Account</Text>
                        <Text className="text-sm text-gray-600">{account.vpa.address}</Text>
                      </View>
                    ) : (
                      <Text className="text-gray-500">Unknown account type</Text>
                    )}
                    
                    <View className="mt-2 flex-row items-center">
                      <View
                        className={`mr-2 h-2 w-2 rounded-full ${
                          account.active ? "bg-green-500" : "bg-red-500"
                        }`}
                      />
                      <Text
                        className={`text-xs ${
                          account.active ? "text-green-600" : "text-red-600"
                        }`}
                      >
                        {account.active ? "Active" : "Inactive"}
                      </Text>
                    </View>
                  </View>

                  <View className="ml-4">
                    {selectedAccountId === account.id ? (
                      <Ionicons name="checkmark-circle" size={24} color="#3B82F6" />
                    ) : (
                      <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View className="flex-1 items-center justify-center py-12">
            <Ionicons name="card-outline" size={64} color="#9CA3AF" />
            <Text className="mt-4 text-lg font-medium text-gray-900">
              No Bank Accounts Found
            </Text>
            <Text className="mt-2 text-center text-gray-600">
              Add a bank account or UPI ID to start receiving withdrawals
            </Text>
            <TouchableOpacity
              className="mt-6 rounded-lg bg-blue-600 px-6 py-3"
              onPress={handleAddAccount}
            >
              <Text className="font-semibold text-white">Add Account</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Gateway Info */}
        <View className="mt-8 rounded-lg bg-gray-50 p-4">
          <Text className="text-sm font-medium text-gray-700">Payout Information</Text>
          <Text className="mt-1 text-xs text-gray-600">
            • Withdrawals are processed using Cashfree gateway with Razorpay fallback
          </Text>
          <Text className="mt-1 text-xs text-gray-600">
            • Bank accounts are securely stored and encrypted
          </Text>
          <Text className="mt-1 text-xs text-gray-600">
            • You can add multiple accounts and choose during withdrawal
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
