import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";

import { api } from "~/utils/api";

interface PayoutTransaction {
  id: string;
  amount: number;
  currency: string;
  status: string;
  transactionType: "CREDIT" | "DEBIT";
  transactionFor: string;
  description?: string;
  paymentGateway: "RAZORPAY" | "CASHFREE";
  createdAt: Date;
  updatedAt?: Date;
}

export default function PayoutHistory() {
  const [refreshing, setRefreshing] = useState(false);

  // Get transaction history (filtering for withdrawals)
  const { data: transactionData, isLoading, refetch } = api.payment.getTransactionHistory.useQuery(
    { limit: 50, offset: 0 }
  );

  const onRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "success":
      case "completed":
        return "text-green-600";
      case "failed":
      case "cancelled":
        return "text-red-600";
      case "pending":
      case "processing":
        return "text-yellow-600";
      default:
        return "text-gray-600";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "success":
      case "completed":
        return "checkmark-circle";
      case "failed":
      case "cancelled":
        return "close-circle";
      case "pending":
      case "processing":
        return "time";
      default:
        return "help-circle";
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-IN", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  // Filter for withdrawal transactions
  const withdrawalTransactions = transactionData?.transactions?.filter(
    (transaction) => 
      transaction.transactionFor === "WITHDRAWAL" || 
      transaction.transactionType === "DEBIT"
  ) || [];

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="mt-4 text-gray-600">Loading payout history...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex-row items-center justify-between border-b border-gray-200 px-4 py-3">
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text className="text-lg font-semibold text-gray-900">Payout History</Text>
        <TouchableOpacity onPress={() => router.push("/payout/withdraw")}>
          <Ionicons name="add" size={24} color="#3B82F6" />
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1"
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {withdrawalTransactions.length > 0 ? (
          <View className="px-4 py-6">
            {withdrawalTransactions.map((transaction) => (
              <TouchableOpacity
                key={transaction.id}
                className="mb-4 rounded-lg border border-gray-200 bg-white p-4 shadow-sm"
                onPress={() => {
                  // Navigate to transaction details
                  router.push(`/payout/transaction/${transaction.id}`);
                }}
              >
                <View className="flex-row items-start justify-between">
                  <View className="flex-1">
                    <View className="flex-row items-center">
                      <Ionicons
                        name={getStatusIcon(transaction.status)}
                        size={20}
                        color={
                          transaction.status === "SUCCESS" ? "#10B981" :
                          transaction.status === "FAILED" ? "#EF4444" :
                          "#F59E0B"
                        }
                      />
                      <Text className="ml-2 font-semibold text-gray-900">
                        Withdrawal
                      </Text>
                    </View>
                    
                    {transaction.description && (
                      <Text className="mt-1 text-sm text-gray-600">
                        {transaction.description}
                      </Text>
                    )}
                    
                    <View className="mt-2 flex-row items-center">
                      <Text className="text-xs text-gray-500">
                        {formatDate(transaction.createdAt)}
                      </Text>
                      <Text className="mx-2 text-xs text-gray-400">•</Text>
                      <Text className="text-xs text-gray-500">
                        {transaction.paymentGateway}
                      </Text>
                    </View>
                  </View>

                  <View className="ml-4 items-end">
                    <Text className="text-lg font-bold text-red-600">
                      -₹{transaction.amount.toLocaleString()}
                    </Text>
                    <Text className={`text-sm font-medium ${getStatusColor(transaction.status)}`}>
                      {transaction.status}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <View className="flex-1 items-center justify-center px-4 py-12">
            <Ionicons name="wallet-outline" size={64} color="#9CA3AF" />
            <Text className="mt-4 text-lg font-medium text-gray-900">
              No Withdrawals Yet
            </Text>
            <Text className="mt-2 text-center text-gray-600">
              Your withdrawal history will appear here once you make your first payout
            </Text>
            <TouchableOpacity
              className="mt-6 rounded-lg bg-blue-600 px-6 py-3"
              onPress={() => router.push("/payout/withdraw")}
            >
              <Text className="font-semibold text-white">Make Withdrawal</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Summary Card */}
        {withdrawalTransactions.length > 0 && (
          <View className="mx-4 mb-6 rounded-lg bg-gray-50 p-4">
            <Text className="text-sm font-medium text-gray-700">Summary</Text>
            <View className="mt-2 flex-row justify-between">
              <Text className="text-xs text-gray-600">Total Withdrawals:</Text>
              <Text className="text-xs font-medium text-gray-900">
                {withdrawalTransactions.length}
              </Text>
            </View>
            <View className="mt-1 flex-row justify-between">
              <Text className="text-xs text-gray-600">Total Amount:</Text>
              <Text className="text-xs font-medium text-gray-900">
                ₹{withdrawalTransactions
                  .reduce((sum, t) => sum + t.amount, 0)
                  .toLocaleString()}
              </Text>
            </View>
            <View className="mt-1 flex-row justify-between">
              <Text className="text-xs text-gray-600">Successful:</Text>
              <Text className="text-xs font-medium text-green-600">
                {withdrawalTransactions.filter(t => t.status === "SUCCESS").length}
              </Text>
            </View>
            <View className="mt-1 flex-row justify-between">
              <Text className="text-xs text-gray-600">Pending:</Text>
              <Text className="text-xs font-medium text-yellow-600">
                {withdrawalTransactions.filter(t => t.status === "PENDING").length}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}
