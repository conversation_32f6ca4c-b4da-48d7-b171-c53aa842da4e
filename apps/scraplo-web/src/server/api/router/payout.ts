import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { UnifiedPayoutService, createPayoutConfig } from "@acme/payouts";
import { eq } from "@acme/db";
import {
  customer,
  customerPaymentTransaction,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// Initialize unified payout service with Cashfree as primary
const payoutConfig = createPayoutConfig({
  PAYOUT_GATEWAY_PRIMARY: "CASHFREE",
  PAYOUT_GATEWAY_FALLBACK: "RAZORPAY",
  ENABLE_PAYOUT_GATEWAY_FALLBACK: "true",
  
  RAZORPAY_KEY_ID: env.RAZORPAY_KEY_ID,
  RAZORPAY_SECRET_KEY: env.RAZORPAY_SECRET_KEY,
  
  CASHFREE_APP_ID: env.CASHFREE_APP_ID,
  CASHFREE_SECRET_KEY: env.CASHFREE_SECRET_KEY,
  CASHFREE_ENVIRONMENT: (env.CASHFREE_ENVIRONMENT as "sandbox" | "production") || "sandbox",
});

const unifiedPayoutService = new UnifiedPayoutService(payoutConfig);

// Schemas
const CreateContactSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email().optional(),
  contact: z.string().optional(),
  type: z.enum(["customer", "vendor", "employee"]).default("customer"),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

const CreateFundAccountSchema = z.object({
  contact_id: z.string(),
  account_type: z.enum(["bank_account", "vpa"]),
  bank_account: z.object({
    name: z.string(),
    account_number: z.string(),
    ifsc: z.string(),
  }).optional(),
  vpa: z.object({
    address: z.string(),
  }).optional(),
}).refine(
  (data) => {
    if (data.account_type === "bank_account" && !data.bank_account) {
      return false;
    }
    if (data.account_type === "vpa" && !data.vpa) {
      return false;
    }
    return true;
  },
  {
    message: "Account details must match the account type",
  }
);

const CreatePayoutSchema = z.object({
  fund_account_id: z.string(),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("INR"),
  mode: z.enum(["IMPS", "NEFT", "RTGS", "UPI"]),
  purpose: z.string(),
  reference_id: z.string().optional(),
  narration: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

export const payoutRouter = createTRPCRouter({
  // Get payout gateway info
  getGatewayInfo: protectedProcedure.query(async () => {
    return unifiedPayoutService.getGatewayInfo();
  }),

  // Contact management
  createContact: protectedProcedure
    .input(CreateContactSchema)
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.createContact(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Fund account management
  createFundAccount: protectedProcedure
    .input(CreateFundAccountSchema)
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.createFundAccount(input);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  getAllFundAccounts: protectedProcedure
    .input(z.object({ contact_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.getAllFundAccounts(input.contact_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data || [];
    }),

  deactivateFundAccount: protectedProcedure
    .input(z.object({ fund_account_id: z.string() }))
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.deactivateFundAccount(input.fund_account_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Payout operations (limited for customers - mainly for refunds)
  createRefundPayout: protectedProcedure
    .input(CreatePayoutSchema.extend({
      order_id: z.string().optional(),
      refund_reason: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      // This would typically be called by admin for customer refunds
      // For now, we'll allow customers to request refunds to their registered accounts
      
      const { data: customerData, err: customerErr } = await tryCatch(
        ctx.db.query.customer.findFirst({
          where: eq(customer.id, ctx.session.user.id),
          columns: {
            walletBalance: true,
          },
        }),
      );

      if (customerErr || !customerData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Customer not found",
        });
      }

      // Create payout for refund
      const { data, error } = await unifiedPayoutService.createPayout({
        fund_account_id: input.fund_account_id,
        amount: input.amount,
        currency: input.currency,
        mode: input.mode,
        purpose: `Refund: ${input.refund_reason}`,
        reference_id: input.reference_id,
        narration: input.narration,
        notes: {
          ...input.notes,
          refund_reason: input.refund_reason,
          order_id: input.order_id || "",
        },
      });
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      // Record refund transaction
      const { err: insertErr } = await tryCatch(
        ctx.db
          .insert(customerPaymentTransaction)
          .values({
            customerId: ctx.session.user.id,
            amount: input.amount.toString(),
            paymentGateway: unifiedPayoutService.getCurrentGateway(),
            status: "PENDING",
            currency: input.currency,
            transactionFor: "REFUND",
            transactionType: "CREDIT",
            gatewayMetadata: {
              payout_id: data?.id,
              fund_account_id: input.fund_account_id,
              refund_reason: input.refund_reason,
              order_id: input.order_id,
            },
          }),
      );

      if (insertErr) {
        console.error("Failed to record refund transaction:", insertErr);
      }

      return data;
    }),

  getPayoutStatus: protectedProcedure
    .input(z.object({ payout_id: z.string() }))
    .query(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.getPayoutStatus(input.payout_id);
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Validation
  validateFundAccount: protectedProcedure
    .input(z.object({
      fund_account_id: z.string(),
      amount: z.number().positive(),
      currency: z.string().default("INR"),
    }))
    .mutation(async ({ input }) => {
      const { data, error } = await unifiedPayoutService.validateFundAccount({
        fund_account: { id: input.fund_account_id },
        amount: input.amount,
        currency: input.currency,
      });
      
      if (error) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: error.message,
        });
      }

      return data;
    }),

  // Get customer wallet balance
  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data: customerData, err } = await tryCatch(
      ctx.db.query.customer.findFirst({
        where: eq(customer.id, ctx.session.user.id),
        columns: {
          walletBalance: true,
        },
      }),
    );

    if (err || !customerData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Customer not found",
      });
    }

    return {
      balance: Number(customerData.walletBalance),
      currency: "INR",
    };
  }),

  // Get refund history
  getRefundHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        offset: z.number().min(0).default(0),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: transactions, err } = await tryCatch(
        ctx.db.query.customerPaymentTransaction.findMany({
          where: eq(customerPaymentTransaction.customerId, ctx.session.user.id),
          orderBy: (transactions, { desc }) => [desc(transactions.createdAt)],
          limit: input.limit,
          offset: input.offset,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch refund history",
        });
      }

      // Filter only refund transactions
      const refunds = transactions?.filter(t => t.transactionFor === "REFUND") || [];

      return {
        refunds: refunds.map((t) => ({
          id: t.id,
          amount: Number(t.amount),
          currency: t.currency,
          status: t.status,
          refund_reason: t.gatewayMetadata?.refund_reason || "N/A",
          order_id: t.gatewayMetadata?.order_id || null,
          createdAt: t.createdAt,
          updatedAt: t.updatedAt,
        })),
      };
    }),
});
